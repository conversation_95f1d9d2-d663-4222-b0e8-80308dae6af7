// Configuration de l'API
const API_BASE_URL = 'http://localhost:5000/api';
let currentUser = null;
let authToken = null;
let allProjects = [];
let allCommunes = [];
let editingProjectId = null;

// Utilitaires
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-MA', {
        style: 'currency',
        currency: 'MAD'
    }).format(amount || 0);
}

function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    errorElement.textContent = message;
    errorElement.classList.remove('hidden');
}

function hideError(elementId) {
    const errorElement = document.getElementById(elementId);
    errorElement.classList.add('hidden');
}

function showLoading(elementId) {
    const element = document.getElementById(elementId);
    element.innerHTML = '<div class="loading"></div>';
}

// Gestion de l'authentification
async function login(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const loginBtn = document.getElementById('loginBtn');
    const loginBtnText = document.getElementById('loginBtnText');
    const loginSpinner = document.getElementById('loginSpinner');
    
    hideError('loginError');
    loginBtn.disabled = true;
    loginBtnText.textContent = 'Connexion...';
    loginSpinner.classList.remove('hidden');
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                nom_utilisateur: username,
                mot_de_passe: password
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            authToken = data.token;
            currentUser = data.user;
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            showMainApp();
        } else {
            showError('loginError', data.message || 'Erreur de connexion');
        }
    } catch (error) {
        console.error('Erreur de connexion:', error);
        showError('loginError', 'Erreur de connexion au serveur');
    } finally {
        loginBtn.disabled = false;
        loginBtnText.textContent = 'Se connecter';
        loginSpinner.classList.add('hidden');
    }
}

function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    
    document.getElementById('loginPage').classList.remove('hidden');
    document.getElementById('mainApp').classList.add('hidden');
    
    // Réinitialiser le formulaire de connexion
    document.getElementById('loginForm').reset();
}

function showMainApp() {
    document.getElementById('loginPage').classList.add('hidden');
    document.getElementById('mainApp').classList.remove('hidden');
    
    // Mettre à jour les informations utilisateur
    document.getElementById('userFullName').textContent = currentUser.nom_complet;
    document.getElementById('userRole').textContent = currentUser.nom_role;
    document.getElementById('welcomeUser').textContent = currentUser.nom_complet;
    
    // Afficher le lien utilisateurs pour les admins
    if (currentUser.est_super_admin || currentUser.nom_role === 'Super Admin') {
        document.getElementById('usersLink').classList.remove('hidden');
    }
    
    // Charger les données initiales
    loadCommunes();
    showDashboard();
}

// Gestion de la navigation
function showDashboard() {
    hideAllContent();
    document.getElementById('dashboardContent').classList.remove('hidden');
    loadDashboardStats();
    loadRecentProjects();
}

function showProjects() {
    hideAllContent();
    document.getElementById('projectsContent').classList.remove('hidden');
    loadProjects();
}

function showProjectForm(projectId = null) {
    hideAllContent();
    document.getElementById('projectFormContent').classList.remove('hidden');
    
    editingProjectId = projectId;
    const formTitle = document.getElementById('formTitle');
    const saveBtn = document.getElementById('saveProjectBtnText');
    
    if (projectId) {
        formTitle.textContent = 'Modifier le projet';
        saveBtn.textContent = 'Modifier';
        loadProjectForEdit(projectId);
    } else {
        formTitle.textContent = 'Nouveau projet';
        saveBtn.textContent = 'Sauvegarder';
        document.getElementById('projectForm').reset();
        document.getElementById('annee').value = new Date().getFullYear();
    }
    
    hideError('projectFormError');
}

function showUsers() {
    if (!currentUser.est_super_admin && currentUser.nom_role !== 'Super Admin') {
        alert('Accès non autorisé');
        return;
    }
    
    hideAllContent();
    document.getElementById('usersContent').classList.remove('hidden');
    loadUsers();
}

function hideAllContent() {
    document.getElementById('dashboardContent').classList.add('hidden');
    document.getElementById('projectsContent').classList.add('hidden');
    document.getElementById('projectFormContent').classList.add('hidden');
    document.getElementById('usersContent').classList.add('hidden');
}

// Fonctions API
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        },
        ...options
    };
    
    try {
        const response = await fetch(url, config);
        
        if (response.status === 401) {
            logout();
            return null;
        }
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erreur API');
        }
        
        return data;
    } catch (error) {
        console.error('Erreur API:', error);
        throw error;
    }
}

// Chargement des données
async function loadDashboardStats() {
    try {
        const stats = await apiCall('/dashboard/stats');
        if (stats) {
            document.getElementById('totalProjects').textContent = stats.totalProjets;
            document.getElementById('ongoingProjects').textContent = stats.projetsEnCours;
            document.getElementById('completedProjects').textContent = stats.projetsTermines;
            document.getElementById('totalBudget').textContent = formatCurrency(stats.budgetTotal);
        }
    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
    }
}

async function loadRecentProjects() {
    try {
        showLoading('recentProjectsList');
        const projects = await apiCall('/projects?limit=5');
        
        if (projects && projects.length > 0) {
            const html = projects.map(project => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 mb-4">
                    <div class="flex-1">
                        <h3 class="font-medium text-gray-900">${project.intitule_projet}</h3>
                        <div class="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                            <span>${project.programme || 'N/A'}</span>
                            <span>•</span>
                            <span>${project.annee}</span>
                            <span>•</span>
                            <span class="flex items-center">
                                <i data-lucide="map-pin" class="h-3 w-3 mr-1"></i>
                                ${project.nom_commune || 'N/A'}
                            </span>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-medium text-gray-900">${formatCurrency(project.cout_global)}</p>
                        <p class="text-sm text-gray-500">${project.statut}</p>
                    </div>
                </div>
            `).join('');
            
            document.getElementById('recentProjectsList').innerHTML = html;
        } else {
            document.getElementById('recentProjectsList').innerHTML = 
                '<p class="text-gray-500 text-center py-8">Aucun projet trouvé</p>';
        }
        
        lucide.createIcons();
    } catch (error) {
        console.error('Erreur lors du chargement des projets récents:', error);
        document.getElementById('recentProjectsList').innerHTML = 
            '<p class="text-red-500 text-center py-8">Erreur lors du chargement</p>';
    }
}

async function loadProjects() {
    try {
        showLoading('projectsList');
        const projects = await apiCall('/projects');
        allProjects = projects || [];
        displayProjects(allProjects);
    } catch (error) {
        console.error('Erreur lors du chargement des projets:', error);
        document.getElementById('projectsList').innerHTML = 
            '<p class="text-red-500 text-center py-8">Erreur lors du chargement des projets</p>';
    }
}

function displayProjects(projects) {
    if (projects.length === 0) {
        document.getElementById('projectsList').innerHTML = 
            '<div class="p-8 text-center"><p class="text-gray-500">Aucun projet trouvé</p></div>';
        return;
    }
    
    const html = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Projet</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Programme</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Localisation</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${projects.map(project => `
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">${project.intitule_projet}</div>
                                    <div class="text-sm text-gray-500 flex items-center">
                                        <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
                                        ${project.annee}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${project.programme || 'N/A'}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex items-center">
                                    <i data-lucide="map-pin" class="h-3 w-3 mr-1"></i>
                                    ${project.nom_commune || 'N/A'}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i data-lucide="dollar-sign" class="h-3 w-3 mr-1"></i>
                                    ${formatCurrency(project.cout_global)}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.statut)}">
                                    ${project.statut}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="showProjectForm(${project.id_projet})" class="text-blue-600 hover:text-blue-900">
                                        <i data-lucide="edit" class="h-4 w-4"></i>
                                    </button>
                                    <button onclick="deleteProject(${project.id_projet})" class="text-red-600 hover:text-red-900">
                                        <i data-lucide="trash-2" class="h-4 w-4"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('projectsList').innerHTML = html;
    lucide.createIcons();
}

function getStatusColor(status) {
    switch (status) {
        case 'Terminé': return 'bg-green-100 text-green-800';
        case 'En cours': return 'bg-blue-100 text-blue-800';
        case 'Suspendu': return 'bg-red-100 text-red-800';
        default: return 'bg-yellow-100 text-yellow-800';
    }
}

function filterProjects() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    const filteredProjects = allProjects.filter(project => {
        const matchesSearch = project.intitule_projet.toLowerCase().includes(searchTerm) ||
                             (project.programme && project.programme.toLowerCase().includes(searchTerm));
        const matchesStatus = statusFilter === '' || project.statut === statusFilter;
        return matchesSearch && matchesStatus;
    });
    
    displayProjects(filteredProjects);
}

async function loadCommunes() {
    try {
        const communes = await apiCall('/communes');
        allCommunes = communes || [];
        
        const communeSelect = document.getElementById('id_commune');
        communeSelect.innerHTML = '<option value="">Sélectionner une commune</option>';
        
        allCommunes.forEach(commune => {
            const option = document.createElement('option');
            option.value = commune.id_commune;
            option.textContent = `${commune.nom_commune} - ${commune.nom_prefecture}`;
            communeSelect.appendChild(option);
        });
    } catch (error) {
        console.error('Erreur lors du chargement des communes:', error);
    }
}

async function loadProjectForEdit(projectId) {
    try {
        const project = await apiCall(`/projects/${projectId}`);
        if (project) {
            document.getElementById('intitule_projet').value = project.intitule_projet || '';
            document.getElementById('programme').value = project.programme || '';
            document.getElementById('annee').value = project.annee || '';
            document.getElementById('secteur').value = project.secteur || '';
            document.getElementById('id_commune').value = project.id_commune || '';
            document.getElementById('quartiers_ou_douars').value = project.quartiers_ou_douars || '';
            document.getElementById('cout_global').value = project.cout_global || '';
            document.getElementById('nombre_beneficiaires').value = project.nombre_beneficiaires || '';
            document.getElementById('statut').value = project.statut || '';
            document.getElementById('activite_royale').checked = project.activite_royale || false;
            document.getElementById('objectifs').value = project.objectifs || '';
            document.getElementById('consistance').value = project.consistance || '';
        }
    } catch (error) {
        console.error('Erreur lors du chargement du projet:', error);
        showError('projectFormError', 'Erreur lors du chargement du projet');
    }
}

// Gestion des projets
async function saveProject(event) {
    event.preventDefault();
    
    const saveBtn = document.getElementById('saveProjectBtn');
    const saveBtnText = document.getElementById('saveProjectBtnText');
    
    hideError('projectFormError');
    saveBtn.disabled = true;
    saveBtnText.textContent = 'Sauvegarde...';
    
    try {
        const formData = {
            intitule_projet: document.getElementById('intitule_projet').value,
            programme: document.getElementById('programme').value,
            annee: parseInt(document.getElementById('annee').value),
            secteur: document.getElementById('secteur').value,
            id_commune: parseInt(document.getElementById('id_commune').value),
            quartiers_ou_douars: document.getElementById('quartiers_ou_douars').value,
            cout_global: parseFloat(document.getElementById('cout_global').value) || 0,
            nombre_beneficiaires: parseInt(document.getElementById('nombre_beneficiaires').value) || 0,
            statut: document.getElementById('statut').value,
            activite_royale: document.getElementById('activite_royale').checked,
            objectifs: document.getElementById('objectifs').value,
            consistance: document.getElementById('consistance').value
        };
        
        if (editingProjectId) {
            await apiCall(`/projects/${editingProjectId}`, {
                method: 'PUT',
                body: JSON.stringify(formData)
            });
        } else {
            await apiCall('/projects', {
                method: 'POST',
                body: JSON.stringify(formData)
            });
        }
        
        showProjects();
    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        showError('projectFormError', error.message || 'Erreur lors de la sauvegarde');
    } finally {
        saveBtn.disabled = false;
        saveBtnText.textContent = editingProjectId ? 'Modifier' : 'Sauvegarder';
    }
}

async function deleteProject(projectId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce projet ?')) {
        return;
    }
    
    try {
        await apiCall(`/projects/${projectId}`, { method: 'DELETE' });
        loadProjects();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression du projet');
    }
}

async function loadUsers() {
    try {
        showLoading('usersList');
        const users = await apiCall('/users');
        
        if (users && users.length > 0) {
            const html = `
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Localisation</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dernière connexion</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${users.map(user => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <i data-lucide="user" class="h-5 w-5 text-blue-600"></i>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900 flex items-center">
                                                    ${user.nom_complet}
                                                    ${user.est_super_admin ? '<i data-lucide="shield" class="h-4 w-4 text-yellow-500 ml-2"></i>' : ''}
                                                </div>
                                                <div class="text-sm text-gray-500">${user.nom_utilisateur}</div>
                                                ${user.email ? `<div class="text-sm text-gray-500">${user.email}</div>` : ''}
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            ${user.nom_role}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${user.nom_commune || user.nom_prefecture || 'Toutes les régions'}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        ${user.derniere_connexion ? new Date(user.derniere_connexion).toLocaleDateString('fr-FR') : 'Jamais'}
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('usersList').innerHTML = html;
        } else {
            document.getElementById('usersList').innerHTML = 
                '<div class="p-8 text-center"><p class="text-gray-500">Aucun utilisateur trouvé</p></div>';
        }
        
        lucide.createIcons();
    } catch (error) {
        console.error('Erreur lors du chargement des utilisateurs:', error);
        document.getElementById('usersList').innerHTML = 
            '<p class="text-red-500 text-center py-8">Erreur lors du chargement des utilisateurs</p>';
    }
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si l'utilisateur est déjà connecté
    const savedToken = localStorage.getItem('authToken');
    const savedUser = localStorage.getItem('currentUser');
    
    if (savedToken && savedUser) {
        authToken = savedToken;
        currentUser = JSON.parse(savedUser);
        showMainApp();
    }
    
    // Gestionnaires d'événements
    document.getElementById('loginForm').addEventListener('submit', login);
    document.getElementById('projectForm').addEventListener('submit', saveProject);
    
    // Initialiser les icônes
    lucide.createIcons();
});
