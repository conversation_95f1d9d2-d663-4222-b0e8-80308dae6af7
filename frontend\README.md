# Frontend - Système de Gestion de Projets

Ce dossier contient le frontend de l'application de gestion de projets. Il s'agit d'une interface web moderne qui fonctionne avec votre serveur web existant (Apache, Nginx, etc.).

## 🚀 **Accès rapide**

Si vous avez un serveur web local (XAMPP, WAMP, etc.) :

1. **Copiez le dossier `frontend`** dans votre répertoire web (htdocs, www, etc.)
2. **Accédez à** : `http://localhost/frontend/`
3. **Connectez-vous avec** : `admin` / `admin123`

## 📋 **Prérequis**

### **Backend requis :**
- Le serveur Node.js backend doit être démarré sur le port 5000
- La base de données MySQL doit être configurée
- L'utilisateur admin doit être créé

### **Serveur web :**
- Apache, Nginx, ou tout serveur web supportant PHP (optionnel)
- Ou simplement ouvrir `index.html` dans un navigateur

## 🔧 **Installation**

### **Option 1 : Avec serveur web (Recommandé)**

1. **Copiez les fichiers** dans votre répertoire web :
   ```
   htdocs/
   └── frontend/
       ├── index.php
       ├── index.html
       ├── app.js
       └── config.php
   ```

2. **Accédez à** : `http://localhost/frontend/`

### **Option 2 : Sans serveur web**

1. **Ouvrez directement** le fichier `index.html` dans votre navigateur
2. **Ou utilisez un serveur local simple** :
   ```bash
   # Avec Python 3
   python -m http.server 8000
   
   # Avec Node.js (si installé)
   npx serve .
   ```

## 🎯 **Fonctionnalités**

### **✅ Implémentées :**
- **Authentification** avec JWT
- **Tableau de bord** avec statistiques
- **Gestion des projets** (liste, création, modification, suppression)
- **Filtrage et recherche** des projets
- **Gestion des utilisateurs** (pour les admins)
- **Interface responsive** et moderne
- **Contrôle d'accès** basé sur les rôles

### **🎨 Interface :**
- Design moderne avec Tailwind CSS
- Icônes Lucide React
- Interface responsive (mobile-friendly)
- Thème professionnel bleu/gris

## 🔐 **Connexion**

### **Compte par défaut :**
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

### **Comptes de démonstration** (si demo-data.sql est exécuté) :
- **Admin Khénifra :** `admin_khenifra` / `demo123`
- **Admin Béni Mellal :** `admin_benimellal` / `demo123`
- **Admin Commune :** `admin_commune_khenifra` / `demo123`
- **Utilisateur Standard :** `user_standard` / `demo123`

## 🛠 **Configuration**

### **Modifier l'URL de l'API :**

Dans `app.js`, ligne 2 :
```javascript
const API_BASE_URL = 'http://localhost:5000/api';
```

Ou dans `config.php` :
```php
define('API_BASE_URL', 'http://localhost:5000/api');
```

### **Personnalisation :**

1. **Couleurs :** Modifiez les classes Tailwind CSS
2. **Logo :** Remplacez l'icône dans la navigation
3. **Titre :** Changez "Gestion de Projets" dans le HTML

## 📱 **Pages disponibles**

1. **Connexion** (`/`) - Authentification utilisateur
2. **Tableau de bord** - Vue d'ensemble et statistiques
3. **Projets** - Liste et gestion des projets
4. **Nouveau projet** - Formulaire de création
5. **Utilisateurs** - Gestion des comptes (admins uniquement)

## 🔍 **Débogage**

### **Problèmes courants :**

1. **Page blanche :**
   - Vérifiez que le backend est démarré
   - Ouvrez la console du navigateur (F12)
   - Vérifiez l'URL de l'API

2. **Erreur de connexion :**
   - Vérifiez que la base de données est configurée
   - Vérifiez que l'utilisateur admin existe
   - Vérifiez les paramètres dans le fichier .env

3. **Pas de projets affichés :**
   - Vérifiez les permissions utilisateur
   - Ajoutez des données de test avec demo-data.sql

### **Console de débogage :**

Ouvrez les outils de développement (F12) pour voir :
- Erreurs JavaScript
- Requêtes API
- Réponses du serveur

## 🌐 **Compatibilité**

### **Navigateurs supportés :**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### **Appareils :**
- Desktop (1200px+)
- Tablette (768px - 1199px)
- Mobile (320px - 767px)

## 📊 **Structure des fichiers**

```
frontend/
├── index.html          # Interface principale (HTML pur)
├── index.php           # Interface avec vérification backend
├── app.js              # Logique JavaScript
├── config.php          # Configuration PHP
└── README.md           # Cette documentation
```

## 🚀 **Déploiement**

### **Pour la production :**

1. **Modifiez l'URL de l'API** pour pointer vers votre serveur de production
2. **Activez HTTPS** pour la sécurité
3. **Configurez CORS** sur le backend pour votre domaine
4. **Optimisez les ressources** (minification, compression)

### **Variables d'environnement :**

Pour la production, créez un fichier `config.js` :
```javascript
const CONFIG = {
    API_BASE_URL: 'https://votre-domaine.com/api',
    APP_NAME: 'Gestion de Projets',
    VERSION: '1.0.0'
};
```

## 📞 **Support**

Pour toute question ou problème :
1. Vérifiez cette documentation
2. Consultez les logs du backend
3. Ouvrez la console du navigateur
4. Vérifiez la configuration de la base de données

## 🔄 **Mises à jour**

Pour mettre à jour le frontend :
1. Sauvegardez vos modifications
2. Remplacez les fichiers
3. Vérifiez la configuration
4. Testez les fonctionnalités
