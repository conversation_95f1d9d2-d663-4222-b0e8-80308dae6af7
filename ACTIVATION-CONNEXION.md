# 🚀 ACTIVATION CONNEXION COMPLÈTE

## 📋 Guide rapide d'activation

### 🎯 **Méthode automatique (Recommandée)**

1. **Double-cliquez** sur `setup-complete.bat`
2. **Suivez les instructions** à l'écran
3. **Double-cliquez** sur `start-system.bat` pour démarrer

### 🔧 **Méthode manuelle**

#### Étape 1: Installer Node.js
- Téléchargez depuis: https://nodejs.org/
- Installez la version LTS
- Redémarrez votre terminal

#### Étape 2: Installer les dépendances
```bash
# Dépendances backend
cd backend
npm install

# Dépendances frontend
cd ..
npm install
```

#### Étape 3: Configurer MySQL
- Consultez `GUIDE-MYSQL.md` pour l'installation
- Créez la base de données:
```bash
mysql -u root -p < database.sql
```

#### Étape 4: Créer l'utilisateur admin
```bash
cd backend
npm run init-db
```

#### Étape 5: Tester la connexion
```bash
cd backend
npm run test-db
```

#### Étape 6: Démarrer les serveurs
```bash
# Terminal 1 - Backend
cd backend
npm start

# Terminal 2 - Frontend
npm run dev
```

## 🌐 **Accès au système**

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000

## 🔐 **Connexion par défaut**

- **Utilisateur**: `admin`
- **Mot de passe**: `admin123`

## ✅ **Vérification du fonctionnement**

### Backend (Port 5000)
- ✅ Serveur démarré sans erreur
- ✅ Connexion MySQL réussie
- ✅ API accessible sur http://localhost:5000

### Frontend (Port 5173)
- ✅ Interface accessible
- ✅ Page de connexion affichée
- ✅ Connexion avec admin/admin123 réussie

## 🚨 **Résolution des problèmes**

### Node.js non trouvé
```bash
# Vérifiez l'installation
node --version
npm --version
```

### Erreur MySQL
```bash
# Testez la connexion
cd backend
npm run test-db
```

### Port déjà utilisé
- Backend: Changez PORT dans .env
- Frontend: Changez port dans vite.config.js

### Erreur de dépendances
```bash
# Réinstallez les dépendances
rm -rf node_modules backend/node_modules
npm install
cd backend && npm install
```

## 📞 **Support**

Si vous rencontrez des problèmes:
1. Consultez `GUIDE-MYSQL.md`
2. Exécutez `npm run test-db` dans le dossier backend
3. Vérifiez les logs dans les terminaux

## 🎉 **Système activé!**

Une fois tout configuré, vous aurez:
- ✅ Authentification complète
- ✅ Gestion des utilisateurs
- ✅ Gestion des projets
- ✅ Interface avec l'image de Khénifra
- ✅ Base de données MySQL fonctionnelle
