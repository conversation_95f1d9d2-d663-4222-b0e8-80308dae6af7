<?php
require_once 'config.php';

// Vérifier si le backend est accessible
$backendOnline = checkBackendStatus();

if (!$backendOnline) {
    showBackendInstructions();
    exit;
}

// Si le backend est accessible, afficher l'application
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion de Projets</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .hidden { display: none !important; }
        .loading {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .status-online {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: #10b981;
            border-radius: 50%;
            margin-right: 8px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Indicateur de statut du backend -->
    <div class="fixed top-4 right-4 z-50 bg-green-100 text-green-800 px-3 py-2 rounded-lg text-sm flex items-center">
        <span class="status-online"></span>
        Backend connecté
    </div>

    <!-- Page de connexion -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
        <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-2xl">
            <div class="text-center">
                <div class="mx-auto h-12 w-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <i data-lucide="log-in" class="h-6 w-6 text-white"></i>
                </div>
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Gestion de Projets</h2>
                <p class="mt-2 text-sm text-gray-600">Connectez-vous à votre compte</p>
            </div>
            
            <form id="loginForm" class="mt-8 space-y-6">
                <div id="loginError" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"></div>
                
                <div class="space-y-4">
                    <div>
                        <label for="username" class="sr-only">Nom d'utilisateur</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="user" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <input id="username" name="username" type="text" required value="admin"
                                class="appearance-none rounded-lg relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder="Nom d'utilisateur">
                        </div>
                    </div>
                    
                    <div>
                        <label for="password" class="sr-only">Mot de passe</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="lock" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <input id="password" name="password" type="password" required value="admin123"
                                class="appearance-none rounded-lg relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder="Mot de passe">
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded text-sm">
                    <strong>Connexion par défaut :</strong><br>
                    Nom d'utilisateur: <code>admin</code><br>
                    Mot de passe: <code>admin123</code>
                </div>

                <div>
                    <button type="submit" id="loginBtn"
                        class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loginBtnText">Se connecter</span>
                        <div id="loginSpinner" class="hidden loading w-5 h-5 ml-2"></div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Application principale -->
    <div id="mainApp" class="hidden min-h-screen bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg border-b">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-8">
                        <h1 class="text-xl font-bold text-blue-600">Gestion de Projets</h1>
                        
                        <div class="hidden md:flex space-x-6">
                            <a href="#" onclick="showDashboard()" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="home" class="h-4 w-4"></i>
                                <span>Tableau de bord</span>
                            </a>
                            
                            <a href="#" onclick="showProjects()" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="folder-open" class="h-4 w-4"></i>
                                <span>Projets</span>
                            </a>
                            
                            <a href="#" onclick="showUsers()" id="usersLink" class="hidden flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="users" class="h-4 w-4"></i>
                                <span>Utilisateurs</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-gray-600">
                            <i data-lucide="user" class="h-4 w-4"></i>
                            <span class="text-sm" id="userFullName"></span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded" id="userRole"></span>
                        </div>
                        
                        <button onclick="logout()" class="flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors">
                            <i data-lucide="log-out" class="h-4 w-4"></i>
                            <span>Déconnexion</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu principal -->
        <main class="container mx-auto px-4 py-8">
            <!-- Tableau de bord -->
            <div id="dashboardContent">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Tableau de bord</h1>
                    <button onclick="showProjectForm()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="plus" class="h-4 w-4"></i>
                        <span>Nouveau projet</span>
                    </button>
                </div>

                <div class="text-sm text-gray-600 mb-6">
                    Bienvenue, <span class="font-medium" id="welcomeUser"></span>
                </div>

                <!-- Statistiques -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="folder-open" class="h-6 w-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Projets</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="trending-up" class="h-6 w-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">En cours</p>
                                <p class="text-2xl font-bold text-gray-900" id="ongoingProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="bar-chart-3" class="h-6 w-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Terminés</p>
                                <p class="text-2xl font-bold text-gray-900" id="completedProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="dollar-sign" class="h-6 w-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Budget Total</p>
                                <p class="text-lg font-bold text-gray-900" id="totalBudget">0 MAD</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projets récents -->
                <div class="bg-white rounded-lg shadow border">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-900">Projets récents</h2>
                            <a href="#" onclick="showProjects()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Voir tous
                            </a>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div id="recentProjectsList">
                            <div class="loading"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Autres contenus (projets, formulaires, etc.) -->
            <!-- Le reste du contenu sera chargé dynamiquement par JavaScript -->
            <div id="projectsContent" class="hidden">
                <div class="text-center py-8">
                    <div class="loading"></div>
                    <p class="text-gray-500 mt-4">Chargement des projets...</p>
                </div>
            </div>

            <div id="projectFormContent" class="hidden">
                <div class="text-center py-8">
                    <p class="text-gray-500">Formulaire de projet</p>
                </div>
            </div>

            <div id="usersContent" class="hidden">
                <div class="text-center py-8">
                    <div class="loading"></div>
                    <p class="text-gray-500 mt-4">Chargement des utilisateurs...</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Configuration de l'API
        const API_BASE_URL = '<?php echo API_BASE_URL; ?>';
    </script>
    <script src="app.js"></script>
    <script>
        // Initialiser les icônes Lucide
        lucide.createIcons();
    </script>
</body>
</html>
