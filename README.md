# Système de Gestion de Projets

Une application web complète pour la gestion de projets avec un système hiérarchique d'administration (Régions > Préfectures > Communes) et un contrôle d'accès basé sur les rôles.

## Fonctionnalités

- **Authentification et autorisation** avec JWT
- **Gestion des projets** (CRUD complet)
- **Système de rôles** (Super Admin, Admin Préfecture, Admin Commune, Utilisateur Standard)
- **Contrôle d'accès hiérarchique** selon la localisation
- **Tableau de bord** avec statistiques
- **Gestion des utilisateurs** (pour les administrateurs)
- **Interface moderne** avec React et Tailwind CSS

## Technologies utilisées

### Frontend
- React 18
- Vite
- React Router DOM
- Axios
- Tailwind CSS
- Lucide React (icônes)

### Backend
- Node.js
- Express.js
- MySQL
- JWT pour l'authentification
- bcryptjs pour le hachage des mots de passe

## Installation

### Prérequis
- Node.js (version 16 ou supérieure)
- MySQL (version 8.0 ou supérieure)
- npm ou yarn

### 1. Cloner le projet
```bash
git clone <url-du-repo>
cd gestion-projets
```

### 2. Installer les dépendances

#### Frontend
```bash
npm install
```

#### Backend
```bash
cd backend
npm install express mysql2 bcryptjs jsonwebtoken cors dotenv
cd ..
```

### 3. Configuration de la base de données

1. Créer une base de données MySQL :
```sql
CREATE DATABASE GestionProjets;
```

2. Exécuter le script SQL pour créer les tables :
```bash
mysql -u root -p GestionProjets < database.sql
```

3. Configurer les variables d'environnement :
```bash
cp .env.example .env
```

Modifier le fichier `.env` avec vos paramètres :
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=votre_mot_de_passe
DB_NAME=GestionProjets
JWT_SECRET=votre_secret_jwt_tres_securise
PORT=5000
```

### 4. Initialiser les données

Créer un utilisateur administrateur par défaut :
```bash
cd backend
node init-db.js
cd ..
```

## Démarrage

### 1. Démarrer le serveur backend
```bash
cd backend
node server.js
```
Le serveur sera accessible sur http://localhost:5000

### 2. Démarrer le frontend (dans un nouveau terminal)
```bash
npm run dev
```
L'application sera accessible sur http://localhost:3000

## Utilisation

### Première connexion
- **Nom d'utilisateur :** `admin`
- **Mot de passe :** `admin123`

⚠️ **Important :** Changez ce mot de passe après la première connexion !

### Structure des rôles

1. **Super Admin** : Accès complet à tous les projets et fonctions administratives
2. **Admin Préfecture** : Peut gérer les projets dans sa préfecture
3. **Admin Commune** : Peut gérer les projets dans sa commune
4. **Utilisateur Standard** : Peut consulter les projets selon son niveau d'accès

### Fonctionnalités principales

#### Tableau de bord
- Vue d'ensemble des statistiques
- Projets récents
- Indicateurs de performance

#### Gestion des projets
- Créer, modifier, supprimer des projets
- Filtrage par statut et recherche
- Localisation par commune
- Suivi des budgets et bénéficiaires

#### Gestion des utilisateurs (Admins uniquement)
- Créer des comptes utilisateurs
- Assigner des rôles et localisations
- Gérer les permissions

## Structure du projet

```
gestion-projets/
├── src/                    # Code source React
│   ├── components/         # Composants React
│   ├── contexts/          # Contextes React (Auth)
│   └── main.jsx           # Point d'entrée
├── backend/               # Code serveur Node.js
│   ├── server.js          # Serveur Express
│   └── init-db.js         # Script d'initialisation
├── database.sql           # Script de création de la BDD
├── package.json           # Dépendances frontend
└── README.md             # Documentation
```

## API Endpoints

### Authentification
- `POST /api/auth/login` - Connexion
- `GET /api/auth/profile` - Profil utilisateur

### Projets
- `GET /api/projects` - Liste des projets
- `POST /api/projects` - Créer un projet
- `GET /api/projects/:id` - Détails d'un projet
- `PUT /api/projects/:id` - Modifier un projet
- `DELETE /api/projects/:id` - Supprimer un projet

### Utilisateurs (Admin uniquement)
- `GET /api/users` - Liste des utilisateurs
- `POST /api/users` - Créer un utilisateur
- `PUT /api/users/:id` - Modifier un utilisateur
- `DELETE /api/users/:id` - Supprimer un utilisateur

### Données de référence
- `GET /api/communes` - Liste des communes
- `GET /api/prefectures` - Liste des préfectures
- `GET /api/roles` - Liste des rôles

### Statistiques
- `GET /api/dashboard/stats` - Statistiques du tableau de bord

## Sécurité

- Authentification JWT
- Hachage des mots de passe avec bcrypt
- Contrôle d'accès basé sur les rôles
- Validation des données côté serveur
- Protection CORS

## Développement

### Scripts disponibles
- `npm run dev` - Démarrage en mode développement
- `npm run build` - Construction pour la production
- `npm run preview` - Aperçu de la version de production

### Structure de la base de données

La base de données suit une structure hiérarchique :
- **Régions** → **Préfectures** → **Communes**
- **Utilisateurs** avec rôles et localisations
- **Projets** avec localisation et suivi détaillé
- **Marchés** liés aux projets
- **Journal d'activités** pour l'audit

## Support

Pour toute question ou problème, veuillez consulter la documentation ou créer une issue dans le repository.

## Licence

Ce projet est sous licence MIT.
