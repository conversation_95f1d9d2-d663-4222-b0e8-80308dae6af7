# 🖼️ Instructions pour ajouter votre image de Khénifra en arrière-plan

## 📥 **Méthode 1 : Utiliser votre image locale**

### **Étape 1 : Télécharger l'image**
1. Cliquez sur votre lien : https://www.google.com/url?sa=i&url=https%3A%2F%2Fwww.mapexpress.ma%2Far%2Factualite%2F...
2. Faites **clic droit** sur l'image → **"Enregistrer l'image sous..."**
3. Sauvegardez-la dans votre dossier projet avec le nom : `khenifra-background.jpg`

### **Étape 2 : Modifier le CSS**
Dans le fichier `index.html`, trouvez cette ligne (vers la ligne 18) :
```css
url('https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80')
```

Remplacez-la par :
```css
url('khenifra-background.jpg')
```

## 🌐 **Méthode 2 : Utiliser une URL directe**

Si vous trouvez l'URL directe de votre image, remplacez simplement l'URL actuelle par la vôtre.

### **Comment trouver l'URL directe :**
1. Ouvrez l'image dans un nouvel onglet
2. Copiez l'URL de la barre d'adresse
3. Assurez-vous qu'elle se termine par `.jpg`, `.png`, ou `.webp`

## 🎨 **Personnalisation avancée**

### **Ajuster la transparence du dégradé :**
Dans le CSS, modifiez les valeurs `rgba` :
```css
background: 
    linear-gradient(135deg, rgba(102, 126, 234, 0.7) 0%, rgba(118, 75, 162, 0.7) 100%),
    url('votre-image.jpg') center/cover no-repeat fixed;
```

- `0.7` = 70% de transparence
- `0.5` = 50% de transparence (plus visible)
- `0.9` = 90% de transparence (moins visible)

### **Changer la position de l'image :**
```css
/* Image centrée (par défaut) */
url('image.jpg') center/cover no-repeat fixed;

/* Image en haut */
url('image.jpg') top/cover no-repeat fixed;

/* Image en bas */
url('image.jpg') bottom/cover no-repeat fixed;
```

## 🔧 **Résolution des problèmes**

### **L'image ne s'affiche pas :**
1. Vérifiez que le nom du fichier est correct
2. Assurez-vous que l'image est dans le même dossier que `index.html`
3. Vérifiez l'extension du fichier (.jpg, .png, etc.)

### **L'image est trop sombre/claire :**
Ajustez la transparence du dégradé (voir section personnalisation)

### **L'image ne couvre pas tout l'écran :**
Assurez-vous d'utiliser `cover` dans le CSS :
```css
url('image.jpg') center/cover no-repeat fixed;
```

## 📱 **Optimisation mobile**

Pour une meilleure performance sur mobile, vous pouvez ajouter :
```css
@media (max-width: 768px) {
    body {
        background-attachment: scroll; /* Au lieu de fixed */
    }
}
```

## ✅ **Résultat attendu**

Après avoir suivi ces instructions, vous devriez avoir :
- Votre image de Khénifra en arrière-plan
- Un dégradé semi-transparent par-dessus
- Des cartes avec effet de verre (glass morphism)
- Un design harmonieux et professionnel

## 🎯 **Conseils pour une belle image**

1. **Résolution recommandée :** 1920x1080 ou plus
2. **Format :** JPG pour les photos, PNG pour les images avec transparence
3. **Taille de fichier :** Moins de 2MB pour un chargement rapide
4. **Qualité :** Bonne qualité mais pas trop lourde

---

**💡 Astuce :** Si vous avez des difficultés, envoyez-moi l'URL directe de votre image et je pourrai vous aider à l'intégrer !
