<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Connexion</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-box {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug du Système de Connexion</h1>
    
    <div class="debug-box">
        <h2>📋 Test de Connexion Direct</h2>
        <div>
            <input type="text" id="username" placeholder="Nom d'utilisateur" value="admin">
            <input type="password" id="password" placeholder="Mot de passe" value="admin123">
            <button onclick="testLogin()">🧪 Tester Connexion</button>
        </div>
        <div id="loginResult"></div>
    </div>

    <div class="debug-box">
        <h2>🔧 Diagnostic Système</h2>
        <div id="diagnostic"></div>
        <button onclick="runDiagnostic()">🔍 Diagnostiquer</button>
    </div>

    <div class="debug-box">
        <h2>📊 Variables JavaScript</h2>
        <div id="variables"></div>
        <button onclick="showVariables()">📊 Afficher Variables</button>
    </div>

    <script>
        // Utilisateurs de test (copie exacte du fichier principal)
        const demoUsers = {
            'admin': {
                password: 'admin123',
                user: {
                    id_utilisateur: 1,
                    nom_utilisateur: 'admin',
                    nom_complet: 'Super Administrateur',
                    email: '<EMAIL>',
                    nom_role: 'Super Admin',
                    est_super_admin: true
                }
            },
            'admin_khenifra': {
                password: 'demo123',
                user: {
                    id_utilisateur: 2,
                    nom_utilisateur: 'admin_khenifra',
                    nom_complet: 'Administrateur Préfecture Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Préfecture',
                    nom_prefecture: 'Khénifra',
                    est_super_admin: false
                }
            },
            'admin_commune': {
                password: 'demo123',
                user: {
                    id_utilisateur: 3,
                    nom_utilisateur: 'admin_commune',
                    nom_complet: 'Administrateur Commune Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Commune',
                    nom_commune: 'Commune Khénifra',
                    est_super_admin: false
                }
            }
        };

        function addStatus(containerId, type, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearStatus(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testLogin() {
            clearStatus('loginResult');
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            addStatus('loginResult', 'info', `🔍 Test avec: ${username} / ${password}`);
            
            // Test exact du code principal
            const demoUser = demoUsers[username];
            
            if (demoUser) {
                addStatus('loginResult', 'success', `✅ Utilisateur trouvé: ${demoUser.user.nom_complet}`);
                
                if (demoUser.password === password) {
                    addStatus('loginResult', 'success', `✅ Mot de passe correct!`);
                    addStatus('loginResult', 'success', `🎉 Connexion réussie pour ${demoUser.user.nom_role}`);
                } else {
                    addStatus('loginResult', 'error', `❌ Mot de passe incorrect. Attendu: ${demoUser.password}`);
                }
            } else {
                addStatus('loginResult', 'error', `❌ Utilisateur non trouvé`);
                addStatus('loginResult', 'info', `💡 Utilisateurs disponibles: ${Object.keys(demoUsers).join(', ')}`);
            }
        }

        function runDiagnostic() {
            clearStatus('diagnostic');
            
            // Test des variables
            addStatus('diagnostic', 'info', `📊 Nombre d'utilisateurs définis: ${Object.keys(demoUsers).length}`);
            
            // Test des fonctions
            if (typeof demoUsers === 'object') {
                addStatus('diagnostic', 'success', '✅ Variable demoUsers définie');
            } else {
                addStatus('diagnostic', 'error', '❌ Variable demoUsers non définie');
            }
            
            // Test du localStorage
            try {
                localStorage.setItem('test', 'ok');
                localStorage.removeItem('test');
                addStatus('diagnostic', 'success', '✅ localStorage fonctionne');
            } catch (e) {
                addStatus('diagnostic', 'error', '❌ localStorage non disponible');
            }
            
            // Test des éléments DOM
            const elements = ['username', 'password'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    addStatus('diagnostic', 'success', `✅ Élément ${id} trouvé`);
                } else {
                    addStatus('diagnostic', 'error', `❌ Élément ${id} non trouvé`);
                }
            });
        }

        function showVariables() {
            const container = document.getElementById('variables');
            container.innerHTML = `
                <div class="code">demoUsers = ${JSON.stringify(demoUsers, null, 2)}</div>
            `;
        }

        // Auto-run au chargement
        window.onload = function() {
            runDiagnostic();
            showVariables();
        };
    </script>
</body>
</html>
