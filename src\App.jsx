import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import Login from './components/Login'
import Dashboard from './components/Dashboard'
import ProjectList from './components/ProjectList'
import ProjectForm from './components/ProjectForm'
import UserManagement from './components/UserManagement'
import Navbar from './components/Navbar'

function ProtectedRoute({ children }) {
  const { user } = useAuth()
  return user ? children : <Navigate to="/login" />
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <ProtectedRoute>
                <div>
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <Dashboard />
                  </main>
                </div>
              </ProtectedRoute>
            } />
            <Route path="/projects" element={
              <ProtectedRoute>
                <div>
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <ProjectList />
                  </main>
                </div>
              </ProtectedRoute>
            } />
            <Route path="/projects/new" element={
              <ProtectedRoute>
                <div>
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <ProjectForm />
                  </main>
                </div>
              </ProtectedRoute>
            } />
            <Route path="/projects/edit/:id" element={
              <ProtectedRoute>
                <div>
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <ProjectForm />
                  </main>
                </div>
              </ProtectedRoute>
            } />
            <Route path="/users" element={
              <ProtectedRoute>
                <div>
                  <Navbar />
                  <main className="container mx-auto px-4 py-8">
                    <UserManagement />
                  </main>
                </div>
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
