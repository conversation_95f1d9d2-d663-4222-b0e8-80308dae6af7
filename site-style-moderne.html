<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع - جهة بني ملال-خنيفرة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: #1e293b;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .logo-text {
            color: #1e293b;
        }

        .logo-text h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 12px;
            color: #64748b;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(71, 85, 105, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(71, 85, 105, 0.4);
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Welcome Section */
        .welcome-section {
            background: white;
            border-radius: 20px;
            padding: 50px 40px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .welcome-section h2 {
            color: #1e293b;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .welcome-section p {
            color: #64748b;
            font-size: 16px;
            line-height: 1.8;
            max-width: 700px;
            margin: 0 auto;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            color: white;
            font-size: 28px;
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
        }

        .feature-card h3 {
            color: #1e293b;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: #64748b;
            font-size: 15px;
            line-height: 1.7;
        }

        /* Second Row Features */
        .features-grid-second {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-buttons {
                flex-direction: column;
                width: 100%;
            }

            .btn {
                justify-content: center;
            }

            .welcome-section {
                padding: 30px 20px;
            }

            .welcome-section h2 {
                font-size: 24px;
            }

            .features-grid,
            .features-grid-second {
                grid-template-columns: 1fr;
            }

            .feature-card {
                padding: 30px 20px;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 20px 15px;
            }

            .welcome-section {
                padding: 25px 15px;
            }

            .feature-card {
                padding: 25px 15px;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }

        /* Animation */
        .feature-card {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .feature-card:nth-child(1) { animation-delay: 0.1s; }
        .feature-card:nth-child(2) { animation-delay: 0.2s; }
        .feature-card:nth-child(3) { animation-delay: 0.3s; }
        .feature-card:nth-child(4) { animation-delay: 0.4s; }
        .feature-card:nth-child(5) { animation-delay: 0.5s; }
        .feature-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-section {
            animation: fadeIn 0.8s ease forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">🏛️</div>
                <div class="logo-text">
                    <h1>نظام إدارة المشاريع</h1>
                    <p>جهة بني ملال-خنيفرة</p>
                </div>
            </div>
            <div class="header-buttons">
                <a href="login.html" class="btn btn-primary">تسجيل الدخول</a>
                <a href="dashboard.html" class="btn btn-secondary">لوحة التحكم</a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>مرحباً بكم في نظام إدارة المشاريع</h2>
            <p>
                نظام شامل لإدارة ومتابعة جميع المشاريع التنموية في جهة بني ملال-خنيفرة. يوفر النظام 
                أدوات متقدمة لتخطيط وتنفيذ ومراقبة المشاريع بكفاءة عالية.
            </p>
        </section>

        <!-- Features Grid - First Row -->
        <section class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>إدارة المشاريع</h3>
                <p>إضافة وتعديل وحذف المشاريع مع تتبع شامل لجميع التفاصيل والمراحل</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🗺️</div>
                <h3>التوطين الجغرافي</h3>
                <p>ربط المشاريع بالمواقع الجغرافية والتقسيمات الإدارية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>إدارة التمويل</h3>
                <p>متابعة مصادر التمويل والميزانيات والنفقات لكل مشروع</p>
            </div>
        </section>

        <!-- Features Grid - Second Row -->
        <section class="features-grid-second">
            <div class="feature-card">
                <div class="feature-icon">📋</div>
                <h3>إدارة الصفقات</h3>
                <p>تتبع الصفقات والمناقصات ومراحل التنفيذ</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>إدارة المستخدمين</h3>
                <p>نظام أدوار متقدم للتحكم في الصلاحيات والوصول</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <h3>التقارير والإحصائيات</h3>
                <p>تقارير شاملة ولوحات معلومات تفاعلية</p>
            </div>
        </section>
    </main>

    <script>
        // Check if user is already logged in
        window.addEventListener('load', function() {
            const token = localStorage.getItem('token');
            if (token) {
                // Verify token is still valid
                fetch('http://localhost:5000/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Update header to show user is logged in
                        const headerButtons = document.querySelector('.header-buttons');
                        headerButtons.innerHTML = `
                            <a href="dashboard.html" class="btn btn-primary">لوحة التحكم</a>
                            <button class="btn btn-secondary" onclick="logout()">تسجيل الخروج</button>
                        `;
                    } else {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                })
                .catch(error => {
                    console.error('Token verification error:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                });
            }
        });

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.reload();
        }

        // Smooth scroll animation for cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
