<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Système de Connexion</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🧪 Test du Système de Connexion</h1>
    
    <div class="test-card">
        <h2>📋 Vérifications Préliminaires</h2>
        <div id="checks"></div>
        <button onclick="runChecks()">🔍 Vérifier le Système</button>
    </div>

    <div class="test-card">
        <h2>🔐 Test de Connexion</h2>
        <div id="loginTest"></div>
        <button onclick="testLogin()">🧪 Tester la Connexion</button>
    </div>

    <div class="test-card">
        <h2>🖼️ Test du Carrousel d'Images</h2>
        <div id="imageTest"></div>
        <button onclick="testImages()">🎨 Vérifier les Images</button>
    </div>

    <div class="test-card">
        <h2>📡 Test de l'API Backend</h2>
        <div id="apiTest"></div>
        <button onclick="testAPI()">🌐 Tester l'API</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';

        function addStatus(containerId, type, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearStatus(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function runChecks() {
            clearStatus('checks');
            
            // Vérifier les fichiers images
            const images = [
                'image.jpg/En-tete-36.png',
                'image.jpg/IMG_9331-504x300.jpg',
                'image.jpg/download.jpg',
                'image.jpg/images.jpg'
            ];

            for (const img of images) {
                try {
                    const response = await fetch(img, { method: 'HEAD' });
                    if (response.ok) {
                        addStatus('checks', 'success', `✅ Image trouvée: ${img}`);
                    } else {
                        addStatus('checks', 'error', `❌ Image manquante: ${img}`);
                    }
                } catch (error) {
                    addStatus('checks', 'error', `❌ Erreur pour ${img}: ${error.message}`);
                }
            }

            // Vérifier index.html
            try {
                const response = await fetch('index.html', { method: 'HEAD' });
                if (response.ok) {
                    addStatus('checks', 'success', '✅ Fichier index.html accessible');
                } else {
                    addStatus('checks', 'error', '❌ Fichier index.html non accessible');
                }
            } catch (error) {
                addStatus('checks', 'error', `❌ Erreur index.html: ${error.message}`);
            }
        }

        async function testLogin() {
            clearStatus('loginTest');
            
            const testCredentials = [
                { username: 'admin', password: 'admin123', name: 'Super Admin' },
                { username: 'admin_khenifra', password: 'demo123', name: 'Admin Préfecture' },
                { username: 'admin_commune', password: 'demo123', name: 'Admin Commune' }
            ];

            for (const cred of testCredentials) {
                try {
                    const response = await fetch(`${API_BASE_URL}/auth/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            nom_utilisateur: cred.username,
                            mot_de_passe: cred.password
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        addStatus('loginTest', 'success', 
                            `✅ ${cred.name}: Connexion réussie (${data.user.nom_complet})`);
                    } else {
                        const error = await response.json();
                        addStatus('loginTest', 'warning', 
                            `⚠️ ${cred.name}: ${error.message}`);
                    }
                } catch (error) {
                    addStatus('loginTest', 'error', 
                        `❌ ${cred.name}: Erreur de connexion - ${error.message}`);
                }
            }
        }

        function testImages() {
            clearStatus('imageTest');
            
            const images = [
                'image.jpg/En-tete-36.png',
                'image.jpg/IMG_9331-504x300.jpg',
                'image.jpg/download.jpg',
                'image.jpg/images.jpg'
            ];

            images.forEach((imgSrc, index) => {
                const img = new Image();
                img.onload = function() {
                    addStatus('imageTest', 'success', 
                        `✅ Image ${index + 1}: ${imgSrc} (${this.width}x${this.height}px)`);
                };
                img.onerror = function() {
                    addStatus('imageTest', 'error', 
                        `❌ Image ${index + 1}: Impossible de charger ${imgSrc}`);
                };
                img.src = imgSrc;
            });

            addStatus('imageTest', 'info', 
                '🎨 Test du carrousel: Les images devraient changer toutes les 3 secondes sur la page principale');
        }

        async function testAPI() {
            clearStatus('apiTest');
            
            // Test de santé de l'API
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    addStatus('apiTest', 'success', '✅ API Backend accessible');
                } else {
                    addStatus('apiTest', 'warning', '⚠️ API Backend répond mais avec erreur');
                }
            } catch (error) {
                addStatus('apiTest', 'error', 
                    `❌ API Backend non accessible: ${error.message}`);
                addStatus('apiTest', 'info', 
                    '💡 Assurez-vous que le serveur backend est démarré (npm start dans le dossier backend)');
            }

            // Test des endpoints
            const endpoints = ['/auth/login', '/projects', '/users'];
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint}`, { method: 'HEAD' });
                    addStatus('apiTest', 'info', 
                        `📡 Endpoint ${endpoint}: ${response.status}`);
                } catch (error) {
                    addStatus('apiTest', 'warning', 
                        `⚠️ Endpoint ${endpoint}: Non accessible`);
                }
            }
        }

        // Auto-run checks on load
        window.onload = function() {
            addStatus('checks', 'info', '🚀 Démarrage des vérifications automatiques...');
            setTimeout(runChecks, 1000);
        };
    </script>
</body>
</html>
