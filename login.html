<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع - تسجيل الدخول</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
            color: #2d3748;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 90%;
            max-width: 900px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 600px;
        }

        .login-left {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .login-left h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .login-left p {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .login-right {
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .form-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-header h2 {
            color: #1a202c;
            font-size: 24px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .form-header p {
            color: #4a5568;
            font-size: 14px;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f7fafc;
            border-radius: 10px;
            padding: 4px;
        }

        .form-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #4a5568;
        }

        .form-tab.active {
            background: white;
            color: #22c55e;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-group label {
            display: block;
            color: #1a202c;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #22c55e;
            background: white;
            box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #22c55e;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .error-message {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .success-message {
            background: #dcfce7;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        .back-to-home {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            color: #22c55e;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
        }

        .back-to-home:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .login-container {
                grid-template-columns: 1fr;
                margin: 20px;
            }

            .login-left {
                padding: 30px 20px;
            }

            .login-right {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .back-to-home {
                position: relative;
                top: auto;
                left: auto;
                margin-bottom: 20px;
                align-self: flex-start;
            }
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-weak { color: #dc2626; }
        .strength-medium { color: #d97706; }
        .strength-strong { color: #16a34a; }

        .form-help {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-to-home">
        ← العودة للرئيسية
    </a>

    <div class="login-container">
        <div class="login-left">
            <div class="logo">🏛️</div>
            <h1>نظام إدارة المشاريع</h1>
            <p>منصة شاملة لإدارة ومتابعة المشاريع التنموية في جهة بني ملال-خنيفرة</p>
        </div>

        <div class="login-right">
            <div class="form-header">
                <h2>مرحباً بك</h2>
                <p>سجل دخولك أو أنشئ حساباً جديداً</p>
            </div>

            <div class="form-tabs">
                <div class="form-tab active" onclick="switchTab('login')">تسجيل الدخول</div>
                <div class="form-tab" onclick="switchTab('register')">حساب جديد</div>
            </div>

            <div id="messages"></div>

            <!-- Login Form -->
            <div id="loginSection" class="form-section active">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginUsername">اسم المستخدم</label>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">كلمة المرور</label>
                        <input type="password" id="loginPassword" name="password" required>
                    </div>
                    
                    <button type="submit" class="submit-btn">دخول</button>
                </form>
            </div>

            <!-- Register Form -->
            <div id="registerSection" class="form-section">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerUsername">اسم المستخدم</label>
                        <input type="text" id="registerUsername" name="username" required>
                        <div class="form-help">يجب أن يكون فريداً ولا يحتوي على مسافات</div>
                    </div>

                    <div class="form-group">
                        <label for="registerFullName">الاسم الكامل</label>
                        <input type="text" id="registerFullName" name="fullName" required>
                    </div>

                    <div class="form-group">
                        <label for="registerEmail">البريد الإلكتروني</label>
                        <input type="email" id="registerEmail" name="email" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="registerPassword">كلمة المرور</label>
                            <input type="password" id="registerPassword" name="password" required>
                            <div id="passwordStrength" class="password-strength"></div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور</label>
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="registerRole">الدور</label>
                            <select id="registerRole" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="4">مستخدم عادي</option>
                                <option value="3">مدير جماعة</option>
                                <option value="2">مدير إقليم</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="registerRegion">الجهة</label>
                            <select id="registerRegion" name="region" required>
                                <option value="">اختر الجهة</option>
                                <option value="1">بني ملال-خنيفرة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="registerPrefecture">الإقليم</label>
                            <select id="registerPrefecture" name="prefecture">
                                <option value="">اختر الإقليم</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="registerCommune">الجماعة</label>
                            <select id="registerCommune" name="commune">
                                <option value="">اختر الجماعة</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn">إنشاء حساب</button>
                </form>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري المعالجة...</p>
            </div>
        </div>
    </div>

    <script>
        // Tab switching
        function switchTab(tab) {
            // Update tab buttons
            document.querySelectorAll('.form-tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');

            // Update form sections
            document.querySelectorAll('.form-section').forEach(s => s.classList.remove('active'));
            document.getElementById(tab + 'Section').classList.add('active');

            // Clear messages
            document.getElementById('messages').innerHTML = '';
        }

        // Password strength checker
        document.getElementById('registerPassword').addEventListener('input', function() {
            const password = this.value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.innerHTML = '';
                return;
            }

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            if (strength < 3) {
                strengthDiv.innerHTML = '<span class="strength-weak">ضعيفة</span>';
            } else if (strength < 4) {
                strengthDiv.innerHTML = '<span class="strength-medium">متوسطة</span>';
            } else {
                strengthDiv.innerHTML = '<span class="strength-strong">قوية</span>';
            }
        });

        // Load prefectures and communes
        async function loadPrefectures() {
            try {
                const response = await fetch('http://localhost:5000/api/prefectures');
                const prefectures = await response.json();
                
                const select = document.getElementById('registerPrefecture');
                select.innerHTML = '<option value="">اختر الإقليم</option>';
                
                prefectures.forEach(prefecture => {
                    const option = document.createElement('option');
                    option.value = prefecture.id_prefecture;
                    option.textContent = prefecture.nom_prefecture;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading prefectures:', error);
            }
        }

        async function loadCommunes(prefectureId) {
            try {
                const response = await fetch(`http://localhost:5000/api/communes/${prefectureId}`);
                const communes = await response.json();
                
                const select = document.getElementById('registerCommune');
                select.innerHTML = '<option value="">اختر الجماعة</option>';
                
                communes.forEach(commune => {
                    const option = document.createElement('option');
                    option.value = commune.id_commune;
                    option.textContent = commune.nom_commune;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading communes:', error);
            }
        }

        // Prefecture change handler
        document.getElementById('registerPrefecture').addEventListener('change', function() {
            const prefectureId = this.value;
            if (prefectureId) {
                loadCommunes(prefectureId);
            } else {
                document.getElementById('registerCommune').innerHTML = '<option value="">اختر الجماعة</option>';
            }
        });

        // Load initial data
        loadPrefectures();

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            await handleSubmit('/api/auth/login', data, 'تسجيل الدخول');
        });

        // Register form handler
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Validate password confirmation
            if (data.password !== data.confirmPassword) {
                showMessage('كلمات المرور غير متطابقة', 'error');
                return;
            }
            
            await handleSubmit('/api/auth/register', data, 'إنشاء الحساب');
        });

        // Generic submit handler
        async function handleSubmit(endpoint, data, action) {
            const messagesDiv = document.getElementById('messages');
            const loadingDiv = document.getElementById('loading');
            
            try {
                loadingDiv.style.display = 'block';
                messagesDiv.innerHTML = '';
                
                const response = await fetch(`http://localhost:5000${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    if (endpoint.includes('login')) {
                        localStorage.setItem('token', result.token);
                        localStorage.setItem('user', JSON.stringify(result.user));
                        
                        showMessage('تم تسجيل الدخول بنجاح! جاري التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    } else {
                        showMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success');
                        switchTab('login');
                        document.getElementById('registerForm').reset();
                    }
                } else {
                    showMessage(result.message || `خطأ في ${action}`, 'error');
                }
            } catch (error) {
                console.error(`${action} error:`, error);
                showMessage('خطأ في الاتصال بالخادم', 'error');
            } finally {
                loadingDiv.style.display = 'none';
            }
        }

        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = `<div class="${type}-message">${message}</div>`;
        }

        // Check if user is already logged in
        window.addEventListener('load', function() {
            const token = localStorage.getItem('token');
            if (token) {
                fetch('http://localhost:5000/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.href = 'dashboard.html';
                    } else {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                })
                .catch(error => {
                    console.error('Token verification error:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                });
            }
        });
    </script>
</body>
</html>
