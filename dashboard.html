<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المشاريع</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
            color: #2d3748;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid #e2e8f0;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }

        .logo-text h1 {
            color: #1a202c;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .logo-text p {
            color: #4a5568;
            font-size: 12px;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            color: #1a202c;
            font-weight: 600;
            font-size: 14px;
        }

        .user-role {
            color: #4a5568;
            font-size: 12px;
        }

        .logout-btn {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(26, 32, 44, 0.3);
        }

        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            max-width: 1400px;
            margin: 0 auto;
            gap: 20px;
            padding: 20px;
            min-height: calc(100vh - 80px);
        }

        /* Sidebar */
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar h3 {
            color: #1a202c;
            font-size: 16px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            color: #4a5568;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-link:hover,
        .nav-link.active {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            transform: translateX(5px);
        }

        .nav-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .content-title {
            color: #1a202c;
            font-size: 24px;
            font-weight: 600;
        }

        .add-btn {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid #e2e8f0;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #22c55e;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #4a5568;
            font-size: 14px;
            font-weight: 500;
        }

        /* Table Styles */
        .table-container {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .data-table th {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 15px 12px;
            text-align: right;
            font-weight: 600;
            font-size: 14px;
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
            font-size: 14px;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background: #3b82f6;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .btn-view {
            background: #6b7280;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* Loading and Messages */
        .loading {
            text-align: center;
            padding: 40px;
            color: #4a5568;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #22c55e;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .error-message {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .success-message {
            background: #dcfce7;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }

            .sidebar {
                position: static;
                order: 2;
            }

            .main-content {
                order: 1;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .table-container {
                padding: 10px;
            }
        }

        /* Hidden by default */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">🏛️</div>
                <div class="logo-text">
                    <h1>نظام إدارة المشاريع</h1>
                    <p>جهة بني ملال-خنيفرة</p>
                </div>
            </div>
            <div class="user-section">
                <div class="user-info">
                    <div class="user-name" id="userName">المستخدم</div>
                    <div class="user-role" id="userRole">الدور</div>
                </div>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="main-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <h3>القوائم الرئيسية</h3>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                        <span class="nav-icon">📊</span>
                        لوحة التحكم
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('projects')">
                        <span class="nav-icon">📋</span>
                        المشاريع
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('regions')">
                        <span class="nav-icon">🗺️</span>
                        الجهات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('prefectures')">
                        <span class="nav-icon">🏛️</span>
                        الأقاليم
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('communes')">
                        <span class="nav-icon">🏘️</span>
                        الجماعات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('users')">
                        <span class="nav-icon">👥</span>
                        المستخدمين
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('financing')">
                        <span class="nav-icon">💰</span>
                        مصادر التمويل
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('markets')">
                        <span class="nav-icon">📄</span>
                        الصفقات
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('logs')">
                        <span class="nav-icon">📝</span>
                        سجل الأنشطة
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div id="messages"></div>

            <!-- Dashboard Section -->
            <div id="dashboardSection" class="content-section active">
                <div class="content-header">
                    <h2 class="content-title">لوحة التحكم</h2>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalProjects">0</div>
                        <div class="stat-label">إجمالي المشاريع</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeProjects">0</div>
                        <div class="stat-label">المشاريع النشطة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalBudget">0</div>
                        <div class="stat-label">إجمالي الميزانية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalUsers">0</div>
                        <div class="stat-label">المستخدمين</div>
                    </div>
                </div>

                <div class="table-container">
                    <h3 style="margin-bottom: 15px; color: #1a202c;">المشاريع الحديثة</h3>
                    <div id="recentProjectsTable">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل البيانات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Projects Section -->
            <div id="projectsSection" class="content-section">
                <div class="content-header">
                    <h2 class="content-title">إدارة المشاريع</h2>
                    <button class="add-btn" onclick="showAddProjectModal()">
                        <span>+</span>
                        إضافة مشروع جديد
                    </button>
                </div>

                <div class="table-container">
                    <div id="projectsTable">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل المشاريع...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other sections will be loaded dynamically -->
            <div id="regionsSection" class="content-section">
                <div class="content-header">
                    <h2 class="content-title">إدارة الجهات</h2>
                    <button class="add-btn" onclick="showAddModal('region')">
                        <span>+</span>
                        إضافة جهة جديدة
                    </button>
                </div>
                <div class="table-container">
                    <div id="regionsTable">
                        <div class="loading">
                            <div class="spinner"></div>
                            <p>جاري تحميل الجهات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add more sections as needed -->
        </main>
    </div>

    <script>
        let currentUser = null;
        let authToken = null;

        // Initialize dashboard
        window.addEventListener('load', function() {
            // Check authentication
            authToken = localStorage.getItem('token');
            const userData = localStorage.getItem('user');
            
            if (!authToken || !userData) {
                window.location.href = 'login.html';
                return;
            }

            currentUser = JSON.parse(userData);
            
            // Update user info in header
            document.getElementById('userName').textContent = currentUser.nom_complet;
            document.getElementById('userRole').textContent = getRoleName(currentUser.nom_role);

            // Load dashboard data
            loadDashboardStats();
            loadRecentProjects();
        });

        function getRoleName(role) {
            const roles = {
                'Super Admin': 'مدير عام',
                'Admin Préfecture': 'مدير إقليم',
                'Admin Commune': 'مدير جماعة',
                'Utilisateur Standard': 'مستخدم عادي'
            };
            return roles[role] || role;
        }

        // Navigation
        function showSection(sectionName) {
            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');

            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName + 'Section').classList.add('active');

            // Load section data
            switch(sectionName) {
                case 'projects':
                    loadProjects();
                    break;
                case 'regions':
                    loadRegions();
                    break;
                case 'prefectures':
                    loadPrefectures();
                    break;
                case 'communes':
                    loadCommunes();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'financing':
                    loadFinancing();
                    break;
                case 'markets':
                    loadMarkets();
                    break;
                case 'logs':
                    loadLogs();
                    break;
            }
        }

        // API helper function
        async function apiCall(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            };

            const response = await fetch(`http://localhost:5000${endpoint}`, {
                ...defaultOptions,
                ...options,
                headers: { ...defaultOptions.headers, ...options.headers }
            });

            if (response.status === 401) {
                logout();
                return;
            }

            return response;
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                const response = await apiCall('/api/dashboard/stats');
                const stats = await response.json();

                document.getElementById('totalProjects').textContent = stats.totalProjects || 0;
                document.getElementById('activeProjects').textContent = stats.activeProjects || 0;
                document.getElementById('totalBudget').textContent = formatCurrency(stats.totalBudget || 0);
                document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        // Load recent projects
        async function loadRecentProjects() {
            try {
                const response = await apiCall('/api/projects/recent');
                const projects = await response.json();

                const tableHtml = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>عنوان المشروع</th>
                                <th>البرنامج</th>
                                <th>السنة</th>
                                <th>الحالة</th>
                                <th>الميزانية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${projects.map(project => `
                                <tr>
                                    <td>${project.intitule_projet}</td>
                                    <td>${project.programme || '-'}</td>
                                    <td>${project.annee || '-'}</td>
                                    <td>${project.statut || '-'}</td>
                                    <td>${formatCurrency(project.cout_global)}</td>
                                    <td>
                                        <button class="action-btn btn-view" onclick="viewProject(${project.id_projet})">عرض</button>
                                        <button class="action-btn btn-edit" onclick="editProject(${project.id_projet})">تعديل</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                document.getElementById('recentProjectsTable').innerHTML = tableHtml;
            } catch (error) {
                console.error('Error loading recent projects:', error);
                document.getElementById('recentProjectsTable').innerHTML = '<p class="error-message">خطأ في تحميل المشاريع</p>';
            }
        }

        // Load projects
        async function loadProjects() {
            try {
                const response = await apiCall('/api/projects');
                const projects = await response.json();

                const tableHtml = `
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الرقم</th>
                                <th>عنوان المشروع</th>
                                <th>البرنامج</th>
                                <th>السنة</th>
                                <th>القطاع</th>
                                <th>الحالة</th>
                                <th>الميزانية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${projects.map(project => `
                                <tr>
                                    <td>${project.id_projet}</td>
                                    <td>${project.intitule_projet}</td>
                                    <td>${project.programme || '-'}</td>
                                    <td>${project.annee || '-'}</td>
                                    <td>${project.secteur || '-'}</td>
                                    <td>${project.statut || '-'}</td>
                                    <td>${formatCurrency(project.cout_global)}</td>
                                    <td>
                                        <button class="action-btn btn-view" onclick="viewProject(${project.id_projet})">عرض</button>
                                        <button class="action-btn btn-edit" onclick="editProject(${project.id_projet})">تعديل</button>
                                        <button class="action-btn btn-delete" onclick="deleteProject(${project.id_projet})">حذف</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                document.getElementById('projectsTable').innerHTML = tableHtml;
            } catch (error) {
                console.error('Error loading projects:', error);
                document.getElementById('projectsTable').innerHTML = '<p class="error-message">خطأ في تحميل المشاريع</p>';
            }
        }

        // Utility functions
        function formatCurrency(amount) {
            if (!amount) return '0 درهم';
            return new Intl.NumberFormat('ar-MA', {
                style: 'currency',
                currency: 'MAD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function showMessage(message, type) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = `<div class="${type}-message">${message}</div>`;
            setTimeout(() => {
                messagesDiv.innerHTML = '';
            }, 5000);
        }

        // Logout function
        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.href = 'login.html';
        }

        // Placeholder functions for other operations
        function loadRegions() {
            console.log('Loading regions...');
        }

        function loadPrefectures() {
            console.log('Loading prefectures...');
        }

        function loadCommunes() {
            console.log('Loading communes...');
        }

        function loadUsers() {
            console.log('Loading users...');
        }

        function loadFinancing() {
            console.log('Loading financing...');
        }

        function loadMarkets() {
            console.log('Loading markets...');
        }

        function loadLogs() {
            console.log('Loading logs...');
        }

        function showAddProjectModal() {
            console.log('Show add project modal...');
        }

        function showAddModal(type) {
            console.log(`Show add ${type} modal...`);
        }

        function viewProject(id) {
            console.log(`View project ${id}`);
        }

        function editProject(id) {
            console.log(`Edit project ${id}`);
        }

        function deleteProject(id) {
            if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
                console.log(`Delete project ${id}`);
            }
        }
    </script>
</body>
</html>
