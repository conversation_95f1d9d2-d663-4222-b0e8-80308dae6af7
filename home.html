<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المشاريع - جهة بني ملال-خنيفرة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fffe 0%, #e8f5e8 100%);
            color: #1a202c;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: white;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid #e2e8f0;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
        }

        .logo-text h1 {
            color: #1a202c;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .logo-text p {
            color: #4a5568;
            font-size: 14px;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .header-btn {
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
        }

        .header-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        .header-btn.secondary {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            box-shadow: 0 4px 15px rgba(26, 32, 44, 0.3);
        }

        .header-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(26, 32, 44, 0.4);
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .welcome-section h2 {
            color: #1a202c;
            font-size: 32px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .welcome-section p {
            color: #4a5568;
            font-size: 16px;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.8;
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }

        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-color: #22c55e;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
        }

        .feature-card h3 {
            color: #1a202c;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .feature-card p {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.6;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: white;
            padding: 40px 0;
            margin-top: 60px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            text-align: center;
        }

        .footer-content h4 {
            color: #22c55e;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .footer-content p {
            color: #a0aec0;
            font-size: 14px;
            line-height: 1.6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .welcome-section {
                padding: 30px 20px;
            }

            .welcome-section h2 {
                font-size: 24px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo">🏛️</div>
                <div class="logo-text">
                    <h1>نظام إدارة المشاريع</h1>
                    <p>جهة بني ملال-خنيفرة</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="login.html" class="header-btn">
                    تسجيل الدخول
                </a>
                <a href="dashboard.html" class="header-btn secondary">
                    لوحة التحكم
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>مرحباً بكم في نظام إدارة المشاريع</h2>
            <p>
                نظام شامل لإدارة ومتابعة جميع المشاريع التنموية في جهة بني ملال-خنيفرة. 
                يوفر النظام أدوات متقدمة لتخطيط وتنفيذ ومراقبة المشاريع بكفاءة عالية.
            </p>
        </section>

        <!-- Features Grid -->
        <section class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>إدارة المشاريع</h3>
                <p>إضافة وتعديل وحذف المشاريع مع تتبع شامل لجميع التفاصيل والمراحل</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🗺️</div>
                <h3>التوطين الجغرافي</h3>
                <p>ربط المشاريع بالمواقع الجغرافية والتقسيمات الإدارية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>إدارة التمويل</h3>
                <p>متابعة مصادر التمويل والميزانيات والنفقات لكل مشروع</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📋</div>
                <h3>إدارة الصفقات</h3>
                <p>تتبع الصفقات والمناقصات ومراحل التنفيذ</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <h3>إدارة المستخدمين</h3>
                <p>نظام أدوار متقدم للتحكم في الصلاحيات والوصول</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📈</div>
                <h3>التقارير والإحصائيات</h3>
                <p>تقارير شاملة ولوحات معلومات تفاعلية</p>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <h4>نظام إدارة المشاريع</h4>
            <p>جهة بني ملال-خنيفرة - المملكة المغربية</p>
            <p>&copy; 2024 جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script>
        // Check if user is already logged in
        window.addEventListener('load', function() {
            const token = localStorage.getItem('token');
            if (token) {
                // Verify token is still valid
                fetch('http://localhost:5000/api/auth/verify', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // Update header to show user is logged in
                        const headerActions = document.querySelector('.header-actions');
                        headerActions.innerHTML = `
                            <a href="dashboard.html" class="header-btn">
                                لوحة التحكم
                            </a>
                            <button class="header-btn secondary" onclick="logout()">
                                تسجيل الخروج
                            </button>
                        `;
                    } else {
                        localStorage.removeItem('token');
                        localStorage.removeItem('user');
                    }
                })
                .catch(error => {
                    console.error('Token verification error:', error);
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                });
            }
        });

        function logout() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            window.location.reload();
        }
    </script>
</body>
</html>
