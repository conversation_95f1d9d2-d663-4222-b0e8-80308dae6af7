const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')
require('dotenv').config()

async function initializeDatabase() {
  const connection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'GestionProjets'
  })

  try {
    console.log('Connexion à la base de données établie...')

    // Vérifier si un super admin existe déjà
    const [existingAdmins] = await connection.execute(
      'SELECT COUNT(*) as count FROM Utilisateurs WHERE est_super_admin = TRUE'
    )

    if (existingAdmins[0].count > 0) {
      console.log('Un super administrateur existe déjà.')
      return
    }

    // Créer un utilisateur super admin par défaut
    const defaultPassword = 'admin123'
    const hashedPassword = await bcrypt.hash(defaultPassword, 10)

    // Récupérer l'ID du rôle Super Admin
    const [roleResult] = await connection.execute(
      'SELECT id_role FROM Roles WHERE nom_role = "Super Admin"'
    )

    if (roleResult.length === 0) {
      console.error('Rôle Super Admin non trouvé dans la base de données')
      return
    }

    const superAdminRoleId = roleResult[0].id_role

    // Insérer l'utilisateur super admin
    await connection.execute(
      `INSERT INTO Utilisateurs (
        nom_utilisateur, mot_de_passe_hash, nom_complet, email,
        id_role, est_super_admin, date_creation
      ) VALUES (?, ?, ?, ?, ?, TRUE, NOW())`,
      [
        'admin',
        hashedPassword,
        'Administrateur Système',
        '<EMAIL>',
        superAdminRoleId
      ]
    )

    console.log('✅ Utilisateur super administrateur créé avec succès!')
    console.log('📧 Nom d\'utilisateur: admin')
    console.log('🔑 Mot de passe: admin123')
    console.log('⚠️  IMPORTANT: Changez ce mot de passe après la première connexion!')

  } catch (error) {
    console.error('Erreur lors de l\'initialisation:', error)
  } finally {
    await connection.end()
  }
}

// Exécuter l'initialisation si ce script est appelé directement
if (require.main === module) {
  initializeDatabase()
}

module.exports = initializeDatabase
