@echo off
echo ========================================
echo   DEMARRAGE SYSTEME GESTION PROJETS
echo ========================================
echo.

echo 🔍 Verification des services...

echo [1/3] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js non trouve! Executez setup-complete.bat d'abord
    pause
    exit /b 1
)
echo ✅ Node.js OK

echo [2/3] Verification des dependances...
if not exist "backend\node_modules" (
    echo ❌ Dependances backend manquantes! Executez setup-complete.bat d'abord
    pause
    exit /b 1
)
if not exist "node_modules" (
    echo ❌ Dependances frontend manquantes! Executez setup-complete.bat d'abord
    pause
    exit /b 1
)
echo ✅ Dependances OK

echo [3/3] Test de connexion MySQL...
echo 📡 Tentative de connexion a la base de donnees...

echo.
echo 🚀 DEMARRAGE DES SERVICES...
echo.

echo 📡 Demarrage du serveur backend (Port 5000)...
start "Backend Server" cmd /k "cd backend && npm start"

timeout /t 3 /nobreak >nul

echo 🌐 Demarrage du serveur frontend (Port 5173)...
start "Frontend Server" cmd /k "npm run dev"

timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ SYSTEME DEMARRE!
echo ========================================
echo.
echo 🌐 Acces au systeme:
echo    Frontend: http://localhost:5173
echo    Backend API: http://localhost:5000
echo.
echo 🔐 CONNEXION:
echo    Utilisateur: admin
echo    Mot de passe: admin123
echo.
echo 📋 Les deux serveurs sont maintenant actifs
echo    Fermez cette fenetre pour arreter les serveurs
echo.

echo 🌐 Ouverture automatique du navigateur...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo Appuyez sur une touche pour fermer tous les serveurs...
pause >nul

echo 🛑 Arret des serveurs...
taskkill /f /im node.exe >nul 2>&1
echo ✅ Serveurs arretes
