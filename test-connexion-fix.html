<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Test Connexion - Version Corrigée</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
            color: #333;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #22c55e;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        h1 {
            color: #dc2626;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #22c55e;
        }
        button {
            width: 100%;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
            transform: translateY(-2px);
        }
        button:disabled {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
        }
        .message {
            padding: 12px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        .success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #16a34a;
            border: 2px solid #22c55e;
            animation: slideIn 0.5s ease-out;
        }
        .error {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
            border: 2px solid #ef4444;
            animation: shake 0.5s ease-in-out;
        }
        .accounts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: left;
            font-size: 14px;
        }
        .hidden {
            display: none;
        }
        .debug {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
            text-align: left;
        }
        @keyframes slideIn {
            0% { transform: translateY(-10px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔧</div>
        <h1>Test de Connexion</h1>
        <p class="subtitle">Version Corrigée - Province de Khénifra</p>
        
        <div id="debug" class="debug">
            🔍 Mode Debug Activé<br>
            Vérification en temps réel...
        </div>
        
        <form id="loginForm">
            <div id="message" class="message hidden"></div>
            
            <div class="form-group">
                <label for="username">👤 Nom d'utilisateur</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">🔒 Mot de passe</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <button type="submit" id="loginBtn">Se connecter</button>
            
            <div class="accounts">
                <strong>🔑 Comptes de test :</strong><br>
                • <strong>Super Admin:</strong> admin / admin123<br>
                • <strong>Admin Préfecture:</strong> admin_khenifra / demo123<br>
                • <strong>Admin Commune:</strong> admin_commune / demo123
            </div>
        </form>
    </div>

    <script>
        // Debug: Afficher les informations de débogage
        function updateDebug(message) {
            const debugDiv = document.getElementById('debug');
            debugDiv.innerHTML += '<br>' + message;
        }

        // Utilisateurs de test (copie exacte)
        const demoUsers = {
            'admin': {
                password: 'admin123',
                user: {
                    id_utilisateur: 1,
                    nom_utilisateur: 'admin',
                    nom_complet: 'Super Administrateur',
                    email: '<EMAIL>',
                    nom_role: 'Super Admin',
                    est_super_admin: true
                }
            },
            'admin_khenifra': {
                password: 'demo123',
                user: {
                    id_utilisateur: 2,
                    nom_utilisateur: 'admin_khenifra',
                    nom_complet: 'Administrateur Préfecture Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Préfecture',
                    nom_prefecture: 'Khénifra',
                    est_super_admin: false
                }
            },
            'admin_commune': {
                password: 'demo123',
                user: {
                    id_utilisateur: 3,
                    nom_utilisateur: 'admin_commune',
                    nom_complet: 'Administrateur Commune Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Commune',
                    nom_commune: 'Commune Khénifra',
                    est_super_admin: false
                }
            }
        };

        updateDebug('✅ Utilisateurs chargés: ' + Object.keys(demoUsers).join(', '));

        // Gestion de la connexion
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            const loginBtn = document.getElementById('loginBtn');
            
            updateDebug(`🔍 Tentative: "${username}" / "${password}"`);
            
            // Reset message
            messageDiv.classList.add('hidden');
            
            // Loading state
            loginBtn.disabled = true;
            loginBtn.textContent = 'Connexion...';
            
            // Test immédiat (sans délai)
            const demoUser = demoUsers[username];
            
            updateDebug(`🔍 Utilisateur trouvé: ${demoUser ? 'OUI' : 'NON'}`);
            
            if (demoUser) {
                updateDebug(`🔍 Mot de passe attendu: "${demoUser.password}"`);
                updateDebug(`🔍 Mot de passe saisi: "${password}"`);
                updateDebug(`🔍 Correspondance: ${demoUser.password === password ? 'OUI' : 'NON'}`);
                
                if (demoUser.password === password) {
                    // Succès
                    messageDiv.className = 'message success';
                    messageDiv.textContent = `✅ Connexion réussie ! Bienvenue ${demoUser.user.nom_complet}`;
                    messageDiv.classList.remove('hidden');
                    
                    updateDebug('✅ CONNEXION RÉUSSIE !');
                    
                    // Simuler redirection
                    setTimeout(() => {
                        alert(`🎉 Connexion réussie !\n\nUtilisateur: ${demoUser.user.nom_complet}\nRôle: ${demoUser.user.nom_role}\n\nVous seriez maintenant redirigé vers le dashboard.`);
                        
                        // Reset pour nouveau test
                        loginBtn.disabled = false;
                        loginBtn.textContent = 'Se connecter';
                        messageDiv.classList.add('hidden');
                    }, 2000);
                } else {
                    // Erreur mot de passe
                    messageDiv.className = 'message error';
                    messageDiv.textContent = `❌ Mot de passe incorrect pour "${username}"`;
                    messageDiv.classList.remove('hidden');
                    
                    updateDebug('❌ MOT DE PASSE INCORRECT');
                    
                    loginBtn.disabled = false;
                    loginBtn.textContent = 'Se connecter';
                }
            } else {
                // Erreur utilisateur
                messageDiv.className = 'message error';
                messageDiv.textContent = `❌ Utilisateur "${username}" non trouvé`;
                messageDiv.classList.remove('hidden');
                
                updateDebug('❌ UTILISATEUR NON TROUVÉ');
                
                loginBtn.disabled = false;
                loginBtn.textContent = 'Se connecter';
            }
        });

        // Test automatique au chargement
        updateDebug('🚀 Page chargée, prêt pour les tests');
        
        // Boutons de test rapide
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === '1') {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
                updateDebug('⚡ Test rapide: admin/admin123');
            }
            if (e.ctrlKey && e.key === '2') {
                document.getElementById('username').value = 'admin_khenifra';
                document.getElementById('password').value = 'demo123';
                updateDebug('⚡ Test rapide: admin_khenifra/demo123');
            }
            if (e.ctrlKey && e.key === '3') {
                document.getElementById('username').value = 'admin_commune';
                document.getElementById('password').value = 'demo123';
                updateDebug('⚡ Test rapide: admin_commune/demo123');
            }
        });
    </script>
</body>
</html>
