<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Test Connexion Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
            color: #333;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #dc2626;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        h1 {
            color: #dc2626;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #dc2626;
        }
        button {
            width: 100%;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }
        button:disabled {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }
        .message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #16a34a;
            border: 2px solid #22c55e;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
            animation: slideInGreen 0.5s ease-out;
        }
        .error {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
            border: 2px solid #ef4444;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
            animation: shakeRed 0.5s ease-in-out;
        }

        @keyframes slideInGreen {
            0% { transform: translateY(-10px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes shakeRed {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .accounts {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: left;
            font-size: 14px;
        }
        .hidden {
            display: none;
        }
        .dashboard {
            display: none;
        }
        .dashboard.active {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Page de connexion -->
    <div class="container" id="loginPage">
        <div class="logo">🏛️</div>
        <h1>Test de Connexion</h1>
        <p class="subtitle">Province de Khénifra</p>

        <form id="loginForm">
            <div id="message" class="message hidden"></div>

            <div class="form-group">
                <label for="username">👤 Nom d'utilisateur</label>
                <input type="text" id="username" required>
            </div>

            <div class="form-group">
                <label for="password">🔒 Mot de passe</label>
                <input type="password" id="password" required>
            </div>

            <button type="submit" id="loginBtn">Se connecter</button>

            <div class="accounts">
                <strong>🔑 Comptes de test :</strong><br>
                • <strong>Super Admin:</strong> admin / admin123<br>
                • <strong>Admin Préfecture:</strong> admin_khenifra / demo123<br>
                • <strong>Admin Commune:</strong> admin_commune / demo123
            </div>
        </form>
    </div>

    <!-- Dashboard -->
    <div class="container dashboard" id="dashboard">
        <div class="logo">✅</div>
        <h1>Connexion Réussie !</h1>
        <p class="subtitle" id="userInfo">Bienvenue</p>

        <div class="message success">
            <strong>🎉 Félicitations !</strong><br>
            Votre système de connexion fonctionne parfaitement.
        </div>

        <button onclick="logout()">🚪 Déconnexion</button>

        <div class="accounts">
            <strong>✅ Fonctionnalités testées :</strong><br>
            • Authentification locale<br>
            • Gestion des rôles<br>
            • Interface responsive<br>
            • Messages d'erreur/succès
        </div>
    </div>

    <script>
        // Utilisateurs de test
        const users = {
            'admin': {
                password: 'admin123',
                name: 'Super Administrateur',
                role: 'Super Admin'
            },
            'admin_khenifra': {
                password: 'demo123',
                name: 'Administrateur Préfecture Khénifra',
                role: 'Admin Préfecture'
            },
            'admin_commune': {
                password: 'demo123',
                name: 'Administrateur Commune Khénifra',
                role: 'Admin Commune'
            }
        };

        // Gestion de la connexion
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            const loginBtn = document.getElementById('loginBtn');

            // Reset message
            messageDiv.classList.add('hidden');

            // Loading state
            loginBtn.disabled = true;
            loginBtn.textContent = 'Connexion...';

            // Simuler un délai
            setTimeout(() => {
                const user = users[username];

                if (user && user.password === password) {
                    // Succès
                    messageDiv.className = 'message success';
                    messageDiv.textContent = `Bienvenue ${user.name} !`;
                    messageDiv.classList.remove('hidden');

                    // Afficher le dashboard
                    setTimeout(() => {
                        document.getElementById('loginPage').style.display = 'none';
                        document.getElementById('dashboard').classList.add('active');
                        document.getElementById('userInfo').textContent = `${user.name} - ${user.role}`;
                    }, 1000);
                } else {
                    // Erreur
                    messageDiv.className = 'message error';
                    messageDiv.textContent = 'Nom d\'utilisateur ou mot de passe incorrect';
                    messageDiv.classList.remove('hidden');
                }

                loginBtn.disabled = false;
                loginBtn.textContent = 'Se connecter';
            }, 500);
        });

        // Déconnexion
        function logout() {
            document.getElementById('dashboard').classList.remove('active');
            document.getElementById('loginPage').style.display = 'block';
            document.getElementById('loginForm').reset();
            document.getElementById('message').classList.add('hidden');
        }

        // Auto-remplissage pour test rapide
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === '1') {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
            }
            if (e.ctrlKey && e.key === '2') {
                document.getElementById('username').value = 'admin_khenifra';
                document.getElementById('password').value = 'demo123';
            }
            if (e.ctrlKey && e.key === '3') {
                document.getElementById('username').value = 'admin_commune';
                document.getElementById('password').value = 'demo123';
            }
        });
    </script>
</body>
</html>
