@echo off
echo ========================================
echo   VERIFICATION SYSTEME COMPLET
echo ========================================
echo.

set "errors=0"

echo [1/7] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js non installe
    set /a errors+=1
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js %%i
)

echo [2/7] Verification de npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm non disponible
    set /a errors+=1
) else (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm %%i
)

echo [3/7] Verification des dependances backend...
if exist "backend\node_modules" (
    echo ✅ Dependances backend installees
) else (
    echo ❌ Dependances backend manquantes
    set /a errors+=1
)

echo [4/7] Verification des dependances frontend...
if exist "node_modules" (
    echo ✅ Dependances frontend installees
) else (
    echo ❌ Dependances frontend manquantes
    set /a errors+=1
)

echo [5/7] Verification du fichier .env...
if exist ".env" (
    echo ✅ Fichier .env present
) else (
    echo ❌ Fichier .env manquant
    set /a errors+=1
)

echo [6/7] Verification de MySQL...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  MySQL non detecte (peut etre normal)
) else (
    echo ✅ MySQL detecte
)

echo [7/7] Verification de l'image de fond...
if exist "image.jpg" (
    echo ✅ Image de fond presente
) else (
    echo ⚠️  Image de fond manquante
)

echo.
echo ========================================
if %errors% equ 0 (
    echo ✅ SYSTEME PRET!
    echo ========================================
    echo.
    echo 🚀 Pour demarrer le systeme:
    echo    Double-cliquez sur start-system.bat
    echo.
    echo 🔧 Ou manuellement:
    echo    1. cd backend ^&^& npm start
    echo    2. npm run dev
    echo.
    echo 🌐 Acces: http://localhost:5173
    echo 🔐 Connexion: admin / admin123
) else (
    echo ❌ %errors% ERREUR(S) DETECTEE(S)!
    echo ========================================
    echo.
    echo 🔧 Actions requises:
    if not exist "backend\node_modules" echo    - Executez: cd backend ^&^& npm install
    if not exist "node_modules" echo    - Executez: npm install
    if not exist ".env" echo    - Creez le fichier .env
    echo.
    echo 📋 Ou executez setup-complete.bat pour tout configurer
)

echo.
pause
