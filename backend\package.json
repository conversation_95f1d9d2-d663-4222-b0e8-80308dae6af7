{"name": "gestion-projets-backend", "version": "1.0.0", "description": "Backend pour le système de gestion de projets", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node init-db.js", "test-db": "node test-connection.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["nodejs", "express", "mysql", "jwt", "gestion-projets"], "author": "Votre nom", "license": "MIT"}