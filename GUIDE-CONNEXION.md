# 🔐 Guide de Connexion - Système de Gestion de Projets

## ✨ Fonctionnalités Ajoutées

### 🎨 **Design avec Images de Khénifra**
- ✅ **Logo officiel** en en-tête (En-tete-36.png)
- ✅ **Carrousel d'images** qui change toutes les 3 secondes
- ✅ **Images de la région** en arrière-plan rotatif
- ✅ **Overlay aux couleurs du Maroc** (rouge et bleu)

### 🔐 **Système de Connexion Complet**
- ✅ **Page de connexion** intégrée avec design Khénifra
- ✅ **Authentification JWT** avec le backend
- ✅ **Gestion des sessions** (localStorage)
- ✅ **Messages d'erreur** et de succès
- ✅ **Redirection automatique** après connexion

### 🖼️ **Carrousel d'Images**
- ✅ **4 images** de la région de Khénifra
- ✅ **Rotation automatique** toutes les 3 secondes
- ✅ **Transition fluide** entre les images
- ✅ **Responsive design** pour tous les écrans

## 🚀 Démarrage Rapide

### **1. Ouvrir le site**
```bash
# Ouvrir index.html dans votre navigateur
# Ou utiliser un serveur local
python -m http.server 8000
# Puis aller sur http://localhost:8000
```

### **2. Tester sans backend**
- ✅ **Carrousel d'images** fonctionne immédiatement
- ✅ **Interface de connexion** s'affiche
- ⚠️ **Connexion** nécessite le backend

### **3. Activer le backend complet**
```bash
# Terminal 1 - Backend
cd backend
npm start

# Terminal 2 - Frontend (optionnel)
npm run dev
```

## 🔑 Comptes de Connexion

### **Comptes par défaut :**
- **Super Admin:** `admin` / `admin123`
- **Admin Préfecture:** `admin_khenifra` / `demo123`
- **Admin Commune:** `admin_commune` / `demo123`

## 🧪 Test du Système

### **Page de test automatique :**
1. **Ouvrez** `test-login.html` dans votre navigateur
2. **Cliquez** sur les boutons de test
3. **Vérifiez** que tout fonctionne

### **Tests manuels :**
1. ✅ **Images** : Vérifiez que le carrousel tourne
2. ✅ **Logo** : Vérifiez que le logo s'affiche
3. ✅ **Connexion** : Testez avec admin/admin123
4. ✅ **Dashboard** : Vérifiez l'accès après connexion

## 📁 Structure des Images

```
image.jpg/
├── En-tete-36.png          # Logo officiel (en-tête)
├── IMG_9331-504x300.jpg    # Image 1 du carrousel
├── download.jpg            # Image 2 du carrousel
└── images.jpg              # Image 3 du carrousel
```

## 🎨 Personnalisation

### **Ajouter des images :**
1. **Placez** vos images dans le dossier `image.jpg/`
2. **Modifiez** le carrousel dans `index.html` :
```html
<div class="background-slide" style="background-image: url('image.jpg/votre-image.jpg')"></div>
```

### **Changer le logo :**
1. **Remplacez** `image.jpg/En-tete-36.png`
2. **Ou modifiez** le chemin dans `index.html`

### **Modifier la vitesse du carrousel :**
```javascript
// Dans index.html, ligne ~840
setInterval(nextSlide, 3000); // 3000ms = 3 secondes
```

## 🔧 Résolution des Problèmes

### **Images ne s'affichent pas :**
- ✅ Vérifiez que les fichiers existent dans `image.jpg/`
- ✅ Vérifiez les noms de fichiers (sensible à la casse)
- ✅ Utilisez `test-login.html` pour diagnostiquer

### **Connexion ne fonctionne pas :**
- ✅ Vérifiez que le backend est démarré (port 5000)
- ✅ Vérifiez la base de données MySQL
- ✅ Testez avec `test-login.html`

### **Carrousel ne tourne pas :**
- ✅ Vérifiez la console du navigateur (F12)
- ✅ Vérifiez que JavaScript est activé
- ✅ Rechargez la page

## 🌐 URLs d'Accès

- **Site principal :** `index.html`
- **Page de test :** `test-login.html`
- **Backend API :** `http://localhost:5000`
- **Frontend dev :** `http://localhost:5173` (si npm run dev)

## 🎉 Fonctionnalités Complètes

✅ **Carrousel d'images** (3 secondes)  
✅ **Logo en en-tête**  
✅ **Connexion complète**  
✅ **Design Khénifra**  
✅ **Responsive**  
✅ **Authentification JWT**  
✅ **Gestion des sessions**  
✅ **Messages d'erreur**  
✅ **Dashboard intégré**  

**🚀 Votre système est maintenant complet et opérationnel !**
