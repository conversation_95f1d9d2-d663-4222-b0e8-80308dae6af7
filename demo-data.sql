-- Données de démonstration pour le système de gestion de projets
-- À exécuter après avoir créé la base de données avec database.sql

USE GestionProjets;

-- Ajouter plus de régions
INSERT INTO Regions (nom_region) VALUES 
('B<PERSON>i <PERSON>lal-Khénifra'),
('Casablanca-Settat'),
('Rabat-Salé-Kénitra'),
('Fès-Meknès'),
('Marrakech-Safi');

-- Ajouter plus de préfectures
INSERT INTO Prefectures (nom_prefecture, id_region) VALUES 
('Béni Mellal', 1),
('<PERSON><PERSON><PERSON>', 1),
('Casablanca', 2),
('Settat', 2),
('<PERSON><PERSON>', 3),
('<PERSON><PERSON>', 3),
('<PERSON><PERSON>', 4),
('Meknès', 4),
('Marrakech', 5),
('Safi', 5);

-- Ajouter plus de communes
INSERT INTO Communes (nom_commune, id_prefecture) VALUES 
-- Khénifra
('Khénifra Centre', 1),
('<PERSON><PERSON><PERSON>', 1),
('<PERSON><PERSON><PERSON>', 1),
-- <PERSON><PERSON><PERSON>
('Béni Mellal Centre', 3),
('<PERSON><PERSON><PERSON><PERSON>', 3),
('<PERSON>s<PERSON> <PERSON><PERSON>', 3),
-- <PERSON>
('Casablanca-Anfa', 5),
('<PERSON> <PERSON>i', 5),
('<PERSON>i <PERSON>oussi', 5),
-- <PERSON><PERSON>
('<PERSON><PERSON> <PERSON>', 7),
('T<PERSON><PERSON>', 7),
('Skhirat', 7);

-- <PERSON><PERSON>er des utilisateurs de d<PERSON>monstration
-- Mot de passe pour tous: "demo123"
-- <PERSON>h bcrypt de "demo123": $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

INSERT INTO Utilisateurs (nom_utilisateur, mot_de_passe_hash, nom_complet, email, id_role, id_prefecture, id_commune, est_super_admin) VALUES
-- Super Admin
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrateur Système', '<EMAIL>', 1, NULL, NULL, TRUE),

-- Admin Préfecture Khénifra
('admin_khenifra', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ahmed Benali', '<EMAIL>', 2, 1, NULL, FALSE),

-- Admin Préfecture Béni Mellal
('admin_benimellal', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Fatima Zahra', '<EMAIL>', 2, 3, NULL, FALSE),

-- Admin Commune Khénifra Centre
('admin_commune_khenifra', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mohamed Alami', '<EMAIL>', 3, NULL, 4, FALSE),

-- Utilisateur Standard
('user_standard', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Youssef Tazi', '<EMAIL>', 4, NULL, NULL, FALSE);

-- Projets de démonstration
INSERT INTO Projets (
    intitule_projet, programme, annee, secteur, objectifs, consistance,
    nombre_beneficiaires, statut_foncier, situation_foncier, stade_etudes,
    activite_royale, cout_global, statut, id_createur
) VALUES
(
    'Construction d\'un centre de santé rural',
    'Programme National de Développement Rural',
    2024,
    'Santé',
    'Améliorer l\'accès aux soins de santé primaires dans les zones rurales',
    'Construction d\'un centre de santé de 500m² avec équipements médicaux de base',
    5000,
    'Terrain public',
    'Disponible',
    'Études techniques terminées',
    FALSE,
    2500000.00,
    'En cours',
    2
),
(
    'Réhabilitation de l\'école primaire Moulay Hassan',
    'Programme d\'Urgence de l\'Éducation',
    2024,
    'Éducation',
    'Améliorer les conditions d\'apprentissage des élèves',
    'Rénovation complète de 12 salles de classe, construction de sanitaires et clôture',
    800,
    'Terrain public',
    'Disponible',
    'Études architecturales en cours',
    FALSE,
    1800000.00,
    'En cours',
    2
),
(
    'Électrification rurale - Douar Ait Brahim',
    'Programme National d\'Électrification Rurale',
    2023,
    'Infrastructure',
    'Raccorder 150 foyers au réseau électrique national',
    'Installation de 8 km de lignes électriques moyenne tension et transformateurs',
    750,
    'Domaine public',
    'Autorisations obtenues',
    'Projet finalisé',
    TRUE,
    3200000.00,
    'Terminé',
    2
),
(
    'Adduction d\'eau potable - Village Tafraout',
    'Programme National d\'Approvisionnement en Eau Potable',
    2024,
    'Eau et Assainissement',
    'Assurer l\'accès à l\'eau potable pour 200 familles',
    'Forage de puits, installation de pompes et réseau de distribution de 5 km',
    1200,
    'Terrain communal',
    'Études hydrogéologiques terminées',
    'Appel d\'offres lancé',
    FALSE,
    4500000.00,
    'En cours',
    4
),
(
    'Aménagement de la route rurale RP 3401',
    'Programme de Désenclavement Rural',
    2024,
    'Transport',
    'Désenclavement de 5 douars et amélioration de l\'accès aux marchés',
    'Revêtement de 15 km de route rurale avec ouvrages d\'art',
    3000,
    'Domaine public',
    'Expropriations en cours',
    'Études d\'impact environnemental',
    FALSE,
    8500000.00,
    'En attente',
    2
);

-- Localisation des projets
INSERT INTO LocalisationProjets (id_projet, id_commune, quartiers_ou_douars) VALUES
(1, 4, 'Douar Ait Moussa, Douar Ait Ali'),
(2, 4, 'Centre ville'),
(3, 5, 'Douar Ait Brahim'),
(4, 4, 'Village Tafraout'),
(5, 4, 'Douars: Ait Youssef, Ait Ahmed, Ait Hassan, Ait Omar, Ait Brahim');

-- Sources de financement
INSERT INTO SourcesFinancement (id_projet, partenaire, montant, annee) VALUES
(1, 'Budget Général de l\'État', 1500000.00, 2024),
(1, 'Conseil Régional', 500000.00, 2024),
(1, 'Initiative Nationale pour le Développement Humain', 500000.00, 2024),
(2, 'Ministère de l\'Éducation Nationale', 1800000.00, 2024),
(3, 'Office National de l\'Électricité', 2400000.00, 2023),
(3, 'Budget Provincial', 800000.00, 2023),
(4, 'Office National de l\'Eau Potable', 3000000.00, 2024),
(4, 'Coopération Internationale', 1500000.00, 2024),
(5, 'Ministère de l\'Équipement', 6000000.00, 2024),
(5, 'Conseil Provincial', 2500000.00, 2024);

-- Marchés (exemples pour quelques projets)
INSERT INTO Marches (
    id_projet, intitule_marche, type_marche, description, credits_ouverts,
    delai_execution_mois, maitre_oeuvre, date_lancement_ao, mode_lancement_ao,
    numero_ao, statut_ao, attributaire, montant_attribution, numero_marche,
    statut_marche, taux_avancement_physique, observations
) VALUES
(
    1, 'Construction du centre de santé rural', 'Marché',
    'Travaux de construction et équipement du centre de santé',
    2500000.00, 18, 'Direction Provinciale de l\'Équipement',
    '2024-01-15', 'Appel d\'offres ouvert', 'AO-2024-001',
    'Attribué', 'Entreprise ATLAS BTP', 2350000.00, 'M-2024-001',
    'En cours', 35.5, 'Travaux de gros œuvre en cours'
),
(
    3, 'Électrification rurale Douar Ait Brahim', 'Marché',
    'Installation du réseau électrique et équipements',
    3200000.00, 12, 'Office National de l\'Électricité',
    '2023-03-01', 'Appel d\'offres restreint', 'AO-2023-015',
    'Réceptionné', 'SOMELEC Maroc', 3100000.00, 'M-2023-015',
    'Terminé', 100.0, 'Projet livré et réceptionné définitivement'
);

-- Quelques entrées dans le journal d'activités
INSERT INTO JournalActivites (id_utilisateur, action, table_concernee, id_enregistrement, nouvelle_valeur, adresse_ip) VALUES
(2, 'CREATE', 'Projets', 1, 'Création du projet: Construction d\'un centre de santé rural', '*************'),
(2, 'CREATE', 'Projets', 2, 'Création du projet: Réhabilitation de l\'école primaire Moulay Hassan', '*************'),
(4, 'CREATE', 'Projets', 4, 'Création du projet: Adduction d\'eau potable - Village Tafraout', '*************'),
(2, 'UPDATE', 'Projets', 1, 'Mise à jour du statut: En cours', '*************'),
(1, 'CREATE', 'Utilisateurs', 5, 'Création de l\'utilisateur: user_standard', '************');
