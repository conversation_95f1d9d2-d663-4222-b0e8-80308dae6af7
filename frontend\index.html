<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion de Projets</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .hidden { display: none !important; }
        .loading {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Page de connexion -->
    <div id="loginPage" class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
        <div class="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-2xl">
            <div class="text-center">
                <div class="mx-auto h-12 w-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <i data-lucide="log-in" class="h-6 w-6 text-white"></i>
                </div>
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Gestion de Projets</h2>
                <p class="mt-2 text-sm text-gray-600">Connectez-vous à votre compte</p>
            </div>
            
            <form id="loginForm" class="mt-8 space-y-6">
                <div id="loginError" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"></div>
                
                <div class="space-y-4">
                    <div>
                        <label for="username" class="sr-only">Nom d'utilisateur</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="user" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <input id="username" name="username" type="text" required
                                class="appearance-none rounded-lg relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder="Nom d'utilisateur">
                        </div>
                    </div>
                    
                    <div>
                        <label for="password" class="sr-only">Mot de passe</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i data-lucide="lock" class="h-5 w-5 text-gray-400"></i>
                            </div>
                            <input id="password" name="password" type="password" required
                                class="appearance-none rounded-lg relative block w-full pl-10 pr-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                placeholder="Mot de passe">
                        </div>
                    </div>
                </div>

                <div>
                    <button type="submit" id="loginBtn"
                        class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="loginBtnText">Se connecter</span>
                        <div id="loginSpinner" class="hidden loading w-5 h-5 ml-2"></div>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Application principale -->
    <div id="mainApp" class="hidden min-h-screen bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-lg border-b">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center space-x-8">
                        <h1 class="text-xl font-bold text-blue-600">Gestion de Projets</h1>
                        
                        <div class="hidden md:flex space-x-6">
                            <a href="#" onclick="showDashboard()" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="home" class="h-4 w-4"></i>
                                <span>Tableau de bord</span>
                            </a>
                            
                            <a href="#" onclick="showProjects()" class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="folder-open" class="h-4 w-4"></i>
                                <span>Projets</span>
                            </a>
                            
                            <a href="#" onclick="showUsers()" id="usersLink" class="hidden flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <i data-lucide="users" class="h-4 w-4"></i>
                                <span>Utilisateurs</span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-gray-600">
                            <i data-lucide="user" class="h-4 w-4"></i>
                            <span class="text-sm" id="userFullName"></span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded" id="userRole"></span>
                        </div>
                        
                        <button onclick="logout()" class="flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors">
                            <i data-lucide="log-out" class="h-4 w-4"></i>
                            <span>Déconnexion</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu principal -->
        <main class="container mx-auto px-4 py-8">
            <!-- Tableau de bord -->
            <div id="dashboardContent">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Tableau de bord</h1>
                    <button onclick="showProjectForm()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="plus" class="h-4 w-4"></i>
                        <span>Nouveau projet</span>
                    </button>
                </div>

                <div class="text-sm text-gray-600 mb-6">
                    Bienvenue, <span class="font-medium" id="welcomeUser"></span>
                </div>

                <!-- Statistiques -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="folder-open" class="h-6 w-6 text-blue-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Projets</p>
                                <p class="text-2xl font-bold text-gray-900" id="totalProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <i data-lucide="trending-up" class="h-6 w-6 text-yellow-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">En cours</p>
                                <p class="text-2xl font-bold text-gray-900" id="ongoingProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="bar-chart-3" class="h-6 w-6 text-green-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Terminés</p>
                                <p class="text-2xl font-bold text-gray-900" id="completedProjects">0</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow border">
                        <div class="flex items-center">
                            <div class="p-2 bg-purple-100 rounded-lg">
                                <i data-lucide="dollar-sign" class="h-6 w-6 text-purple-600"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Budget Total</p>
                                <p class="text-lg font-bold text-gray-900" id="totalBudget">0 MAD</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projets récents -->
                <div class="bg-white rounded-lg shadow border">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-900">Projets récents</h2>
                            <a href="#" onclick="showProjects()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Voir tous
                            </a>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div id="recentProjectsList">
                            <div class="loading"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des projets -->
            <div id="projectsContent" class="hidden">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Projets</h1>
                    <button onclick="showProjectForm()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="plus" class="h-4 w-4"></i>
                        <span>Nouveau projet</span>
                    </button>
                </div>

                <!-- Filtres -->
                <div class="bg-white p-4 rounded-lg shadow border mb-6">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"></i>
                                <input type="text" id="searchInput" placeholder="Rechercher un projet..."
                                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    onkeyup="filterProjects()">
                            </div>
                        </div>
                        <div class="w-full md:w-48">
                            <select id="statusFilter" onchange="filterProjects()"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Tous les statuts</option>
                                <option value="En cours">En cours</option>
                                <option value="Terminé">Terminé</option>
                                <option value="En attente">En attente</option>
                                <option value="Suspendu">Suspendu</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Liste des projets -->
                <div class="bg-white rounded-lg shadow border">
                    <div id="projectsList">
                        <div class="loading"></div>
                    </div>
                </div>
            </div>

            <!-- Formulaire de projet -->
            <div id="projectFormContent" class="hidden">
                <div class="max-w-4xl mx-auto space-y-6">
                    <div class="flex items-center space-x-4">
                        <button onclick="showProjects()" class="p-2 text-gray-600 hover:text-gray-900">
                            <i data-lucide="arrow-left" class="h-5 w-5"></i>
                        </button>
                        <h1 class="text-3xl font-bold text-gray-900" id="formTitle">Nouveau projet</h1>
                    </div>

                    <div id="projectFormError" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded"></div>

                    <form id="projectForm" class="bg-white rounded-lg shadow border p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Intitulé du projet *</label>
                                <input type="text" id="intitule_projet" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Programme</label>
                                <input type="text" id="programme"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Année</label>
                                <input type="number" id="annee" min="2000" max="2050" value="2024"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Secteur</label>
                                <input type="text" id="secteur"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Commune *</label>
                                <select id="id_commune" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">Sélectionner une commune</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Quartiers ou Douars</label>
                                <input type="text" id="quartiers_ou_douars"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Coût global (MAD)</label>
                                <input type="number" id="cout_global" step="0.01"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nombre de bénéficiaires</label>
                                <input type="number" id="nombre_beneficiaires"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Statut</label>
                                <select id="statut"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="En attente">En attente</option>
                                    <option value="En cours">En cours</option>
                                    <option value="Terminé">Terminé</option>
                                    <option value="Suspendu">Suspendu</option>
                                </select>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="activite_royale"
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="activite_royale" class="ml-2 block text-sm text-gray-900">Activité royale</label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Objectifs</label>
                            <textarea id="objectifs" rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Consistance</label>
                            <textarea id="consistance" rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <button type="button" onclick="showProjects()"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                                Annuler
                            </button>
                            <button type="submit" id="saveProjectBtn"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 disabled:opacity-50">
                                <i data-lucide="save" class="h-4 w-4"></i>
                                <span id="saveProjectBtnText">Sauvegarder</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Gestion des utilisateurs -->
            <div id="usersContent" class="hidden">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Gestion des utilisateurs</h1>
                    <button onclick="showUserForm()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
                        <i data-lucide="plus" class="h-4 w-4"></i>
                        <span>Nouvel utilisateur</span>
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow border">
                    <div id="usersList">
                        <div class="loading"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
    <script>
        // Initialiser les icônes Lucide
        lucide.createIcons();
    </script>
</body>
</html>
