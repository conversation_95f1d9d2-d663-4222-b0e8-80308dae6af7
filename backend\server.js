const express = require('express')
const cors = require('cors')
const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
require('dotenv').config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors())
app.use(express.json())

// Configuration de la base de données
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'GestionProjets',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
}

const pool = mysql.createPool(dbConfig)

// Middleware d'authentification
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Token d\'accès requis' })
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret_key')
    const [rows] = await pool.execute(
      `SELECT u.*, r.nom_role, c.nom_commune, p.nom_prefecture
       FROM Utilisateurs u
       LEFT JOIN Roles r ON u.id_role = r.id_role
       LEFT JOIN Communes c ON u.id_commune = c.id_commune
       LEFT JOIN Prefectures p ON u.id_prefecture = p.id_prefecture
       WHERE u.id_utilisateur = ?`,
      [decoded.userId]
    )

    if (rows.length === 0) {
      return res.status(403).json({ message: 'Utilisateur non trouvé' })
    }

    req.user = rows[0]
    next()
  } catch (error) {
    return res.status(403).json({ message: 'Token invalide' })
  }
}

// Routes d'authentification
app.post('/api/auth/login', async (req, res) => {
  try {
    const { nom_utilisateur, mot_de_passe } = req.body

    const [rows] = await pool.execute(
      `SELECT u.*, r.nom_role
       FROM Utilisateurs u
       LEFT JOIN Roles r ON u.id_role = r.id_role
       WHERE u.nom_utilisateur = ?`,
      [nom_utilisateur]
    )

    if (rows.length === 0) {
      return res.status(401).json({ message: 'Nom d\'utilisateur ou mot de passe incorrect' })
    }

    const user = rows[0]
    const isValidPassword = await bcrypt.compare(mot_de_passe, user.mot_de_passe_hash)

    if (!isValidPassword) {
      return res.status(401).json({ message: 'Nom d\'utilisateur ou mot de passe incorrect' })
    }

    // Mettre à jour la dernière connexion
    await pool.execute(
      'UPDATE Utilisateurs SET derniere_connexion = NOW() WHERE id_utilisateur = ?',
      [user.id_utilisateur]
    )

    const token = jwt.sign(
      { userId: user.id_utilisateur },
      process.env.JWT_SECRET || 'secret_key',
      { expiresIn: '24h' }
    )

    const { mot_de_passe_hash, ...userWithoutPassword } = user
    res.json({ token, user: userWithoutPassword })
  } catch (error) {
    console.error('Erreur de connexion:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.get('/api/auth/profile', authenticateToken, (req, res) => {
  const { mot_de_passe_hash, ...userWithoutPassword } = req.user
  res.json(userWithoutPassword)
})

// Routes pour les projets
app.get('/api/projects', authenticateToken, async (req, res) => {
  try {
    const { limit } = req.query
    const user = req.user

    let query = `
      SELECT p.*,
             c.nom_commune,
             pr.nom_prefecture,
             u.nom_complet as createur_nom
      FROM Projets p
      LEFT JOIN LocalisationProjets lp ON p.id_projet = lp.id_projet
      LEFT JOIN Communes c ON lp.id_commune = c.id_commune
      LEFT JOIN Prefectures pr ON c.id_prefecture = pr.id_prefecture
      LEFT JOIN Utilisateurs u ON p.id_createur = u.id_utilisateur
    `

    let whereConditions = []
    let params = []

    // Filtrage selon le rôle de l'utilisateur
    if (!user.est_super_admin && user.nom_role !== 'Super Admin') {
      if (user.nom_role === 'Admin Préfecture' && user.id_prefecture) {
        whereConditions.push('pr.id_prefecture = ?')
        params.push(user.id_prefecture)
      } else if (user.nom_role === 'Admin Commune' && user.id_commune) {
        whereConditions.push('c.id_commune = ?')
        params.push(user.id_commune)
      }
    }

    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ')
    }

    query += ' ORDER BY p.date_creation DESC'

    if (limit) {
      query += ' LIMIT ?'
      params.push(parseInt(limit))
    }

    const [rows] = await pool.execute(query, params)
    res.json(rows)
  } catch (error) {
    console.error('Erreur lors de la récupération des projets:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.get('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT p.*, lp.id_commune, lp.quartiers_ou_douars
       FROM Projets p
       LEFT JOIN LocalisationProjets lp ON p.id_projet = lp.id_projet
       WHERE p.id_projet = ?`,
      [req.params.id]
    )

    if (rows.length === 0) {
      return res.status(404).json({ message: 'Projet non trouvé' })
    }

    res.json(rows[0])
  } catch (error) {
    console.error('Erreur lors de la récupération du projet:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.post('/api/projects', authenticateToken, async (req, res) => {
  const connection = await pool.getConnection()
  try {
    await connection.beginTransaction()

    const {
      intitule_projet,
      programme,
      annee,
      secteur,
      objectifs,
      consistance,
      nombre_beneficiaires,
      statut_foncier,
      situation_foncier,
      stade_etudes,
      activite_royale,
      cout_global,
      statut,
      id_commune,
      quartiers_ou_douars
    } = req.body

    // Insérer le projet
    const [result] = await connection.execute(
      `INSERT INTO Projets (
        intitule_projet, programme, annee, secteur, objectifs, consistance,
        nombre_beneficiaires, statut_foncier, situation_foncier, stade_etudes,
        activite_royale, cout_global, statut, id_createur
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        intitule_projet, programme, annee, secteur, objectifs, consistance,
        nombre_beneficiaires, statut_foncier, situation_foncier, stade_etudes,
        activite_royale, cout_global, statut, req.user.id_utilisateur
      ]
    )

    const projectId = result.insertId

    // Insérer la localisation
    if (id_commune) {
      await connection.execute(
        'INSERT INTO LocalisationProjets (id_projet, id_commune, quartiers_ou_douars) VALUES (?, ?, ?)',
        [projectId, id_commune, quartiers_ou_douars]
      )
    }

    await connection.commit()
    res.status(201).json({ id: projectId, message: 'Projet créé avec succès' })
  } catch (error) {
    await connection.rollback()
    console.error('Erreur lors de la création du projet:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  } finally {
    connection.release()
  }
})

app.put('/api/projects/:id', authenticateToken, async (req, res) => {
  const connection = await pool.getConnection()
  try {
    await connection.beginTransaction()

    const {
      intitule_projet,
      programme,
      annee,
      secteur,
      objectifs,
      consistance,
      nombre_beneficiaires,
      statut_foncier,
      situation_foncier,
      stade_etudes,
      activite_royale,
      cout_global,
      statut,
      id_commune,
      quartiers_ou_douars
    } = req.body

    // Mettre à jour le projet
    await connection.execute(
      `UPDATE Projets SET
        intitule_projet = ?, programme = ?, annee = ?, secteur = ?, objectifs = ?,
        consistance = ?, nombre_beneficiaires = ?, statut_foncier = ?, situation_foncier = ?,
        stade_etudes = ?, activite_royale = ?, cout_global = ?, statut = ?
       WHERE id_projet = ?`,
      [
        intitule_projet, programme, annee, secteur, objectifs, consistance,
        nombre_beneficiaires, statut_foncier, situation_foncier, stade_etudes,
        activite_royale, cout_global, statut, req.params.id
      ]
    )

    // Mettre à jour la localisation
    await connection.execute(
      'DELETE FROM LocalisationProjets WHERE id_projet = ?',
      [req.params.id]
    )

    if (id_commune) {
      await connection.execute(
        'INSERT INTO LocalisationProjets (id_projet, id_commune, quartiers_ou_douars) VALUES (?, ?, ?)',
        [req.params.id, id_commune, quartiers_ou_douars]
      )
    }

    await connection.commit()
    res.json({ message: 'Projet mis à jour avec succès' })
  } catch (error) {
    await connection.rollback()
    console.error('Erreur lors de la mise à jour du projet:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  } finally {
    connection.release()
  }
})

app.delete('/api/projects/:id', authenticateToken, async (req, res) => {
  try {
    await pool.execute('DELETE FROM Projets WHERE id_projet = ?', [req.params.id])
    res.json({ message: 'Projet supprimé avec succès' })
  } catch (error) {
    console.error('Erreur lors de la suppression du projet:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

// Routes pour les données de référence
app.get('/api/communes', authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT c.*, p.nom_prefecture
       FROM Communes c
       LEFT JOIN Prefectures p ON c.id_prefecture = p.id_prefecture
       ORDER BY p.nom_prefecture, c.nom_commune`
    )
    res.json(rows)
  } catch (error) {
    console.error('Erreur lors de la récupération des communes:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.get('/api/prefectures', authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM Prefectures ORDER BY nom_prefecture')
    res.json(rows)
  } catch (error) {
    console.error('Erreur lors de la récupération des préfectures:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.get('/api/roles', authenticateToken, async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM Roles ORDER BY nom_role')
    res.json(rows)
  } catch (error) {
    console.error('Erreur lors de la récupération des rôles:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

// Route pour les statistiques du tableau de bord
app.get('/api/dashboard/stats', authenticateToken, async (req, res) => {
  try {
    const user = req.user
    let whereClause = ''
    let params = []

    // Filtrage selon le rôle de l'utilisateur
    if (!user.est_super_admin && user.nom_role !== 'Super Admin') {
      if (user.nom_role === 'Admin Préfecture' && user.id_prefecture) {
        whereClause = `WHERE c.id_prefecture = ?`
        params.push(user.id_prefecture)
      } else if (user.nom_role === 'Admin Commune' && user.id_commune) {
        whereClause = `WHERE lp.id_commune = ?`
        params.push(user.id_commune)
      }
    }

    const query = `
      SELECT
        COUNT(*) as totalProjets,
        COUNT(CASE WHEN p.statut = 'En cours' THEN 1 END) as projetsEnCours,
        COUNT(CASE WHEN p.statut = 'Terminé' THEN 1 END) as projetsTermines,
        COALESCE(SUM(p.cout_global), 0) as budgetTotal
      FROM Projets p
      LEFT JOIN LocalisationProjets lp ON p.id_projet = lp.id_projet
      LEFT JOIN Communes c ON lp.id_commune = c.id_commune
      ${whereClause}
    `

    const [rows] = await pool.execute(query, params)
    res.json(rows[0])
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

// Routes pour la gestion des utilisateurs
app.get('/api/users', authenticateToken, async (req, res) => {
  try {
    // Vérifier si l'utilisateur a les droits d'administration
    if (!req.user.est_super_admin && req.user.nom_role !== 'Super Admin') {
      return res.status(403).json({ message: 'Accès non autorisé' })
    }

    const [rows] = await pool.execute(
      `SELECT u.*, r.nom_role, c.nom_commune, p.nom_prefecture
       FROM Utilisateurs u
       LEFT JOIN Roles r ON u.id_role = r.id_role
       LEFT JOIN Communes c ON u.id_commune = c.id_commune
       LEFT JOIN Prefectures p ON u.id_prefecture = p.id_prefecture
       ORDER BY u.nom_complet`
    )

    // Retirer les mots de passe des résultats
    const usersWithoutPasswords = rows.map(user => {
      const { mot_de_passe_hash, ...userWithoutPassword } = user
      return userWithoutPassword
    })

    res.json(usersWithoutPasswords)
  } catch (error) {
    console.error('Erreur lors de la récupération des utilisateurs:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.post('/api/users', authenticateToken, async (req, res) => {
  try {
    // Vérifier si l'utilisateur a les droits d'administration
    if (!req.user.est_super_admin && req.user.nom_role !== 'Super Admin') {
      return res.status(403).json({ message: 'Accès non autorisé' })
    }

    const {
      nom_utilisateur,
      mot_de_passe,
      nom_complet,
      email,
      id_role,
      id_prefecture,
      id_commune,
      est_super_admin
    } = req.body

    // Vérifier si le nom d'utilisateur existe déjà
    const [existingUser] = await pool.execute(
      'SELECT id_utilisateur FROM Utilisateurs WHERE nom_utilisateur = ?',
      [nom_utilisateur]
    )

    if (existingUser.length > 0) {
      return res.status(400).json({ message: 'Ce nom d\'utilisateur existe déjà' })
    }

    // Hasher le mot de passe
    const hashedPassword = await bcrypt.hash(mot_de_passe, 10)

    // Insérer le nouvel utilisateur
    await pool.execute(
      `INSERT INTO Utilisateurs (
        nom_utilisateur, mot_de_passe_hash, nom_complet, email,
        id_role, id_prefecture, id_commune, est_super_admin
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        nom_utilisateur, hashedPassword, nom_complet, email,
        id_role, id_prefecture || null, id_commune || null, est_super_admin
      ]
    )

    res.status(201).json({ message: 'Utilisateur créé avec succès' })
  } catch (error) {
    console.error('Erreur lors de la création de l\'utilisateur:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.put('/api/users/:id', authenticateToken, async (req, res) => {
  try {
    // Vérifier si l'utilisateur a les droits d'administration
    if (!req.user.est_super_admin && req.user.nom_role !== 'Super Admin') {
      return res.status(403).json({ message: 'Accès non autorisé' })
    }

    const {
      nom_utilisateur,
      mot_de_passe,
      nom_complet,
      email,
      id_role,
      id_prefecture,
      id_commune,
      est_super_admin
    } = req.body

    let updateQuery = `
      UPDATE Utilisateurs SET
        nom_utilisateur = ?, nom_complet = ?, email = ?,
        id_role = ?, id_prefecture = ?, id_commune = ?, est_super_admin = ?
    `
    let params = [
      nom_utilisateur, nom_complet, email,
      id_role, id_prefecture || null, id_commune || null, est_super_admin
    ]

    // Si un nouveau mot de passe est fourni, l'inclure dans la mise à jour
    if (mot_de_passe && mot_de_passe.trim() !== '') {
      const hashedPassword = await bcrypt.hash(mot_de_passe, 10)
      updateQuery += ', mot_de_passe_hash = ?'
      params.push(hashedPassword)
    }

    updateQuery += ' WHERE id_utilisateur = ?'
    params.push(req.params.id)

    await pool.execute(updateQuery, params)
    res.json({ message: 'Utilisateur mis à jour avec succès' })
  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'utilisateur:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.delete('/api/users/:id', authenticateToken, async (req, res) => {
  try {
    // Vérifier si l'utilisateur a les droits d'administration
    if (!req.user.est_super_admin && req.user.nom_role !== 'Super Admin') {
      return res.status(403).json({ message: 'Accès non autorisé' })
    }

    // Empêcher la suppression de son propre compte
    if (parseInt(req.params.id) === req.user.id_utilisateur) {
      return res.status(400).json({ message: 'Vous ne pouvez pas supprimer votre propre compte' })
    }

    await pool.execute('DELETE FROM Utilisateurs WHERE id_utilisateur = ?', [req.params.id])
    res.json({ message: 'Utilisateur supprimé avec succès' })
  } catch (error) {
    console.error('Erreur lors de la suppression de l\'utilisateur:', error)
    res.status(500).json({ message: 'Erreur serveur' })
  }
})

app.listen(PORT, () => {
  console.log(`Serveur démarré sur le port ${PORT}`)
})
