<?php
// Configuration pour le frontend
header('Content-Type: text/html; charset=utf-8');

// Configuration de l'API backend
define('API_BASE_URL', 'http://localhost:5000/api');

// Vérifier si le backend est accessible
function checkBackendStatus() {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, API_BASE_URL . '/health');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}

// Fonction pour afficher les instructions si le backend n'est pas accessible
function showBackendInstructions() {
    echo '
    <div style="max-width: 600px; margin: 50px auto; padding: 20px; font-family: Arial, sans-serif; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #dc3545;">
        <h2 style="color: #dc3545; margin-top: 0;">⚠️ Backend non accessible</h2>
        <p>Le serveur backend n\'est pas démarré. Pour utiliser l\'application, vous devez :</p>
        
        <ol style="line-height: 1.6;">
            <li><strong>Installer Node.js</strong> depuis <a href="https://nodejs.org/" target="_blank">nodejs.org</a></li>
            <li><strong>Ouvrir un terminal</strong> dans le dossier du projet</li>
            <li><strong>Installer les dépendances backend :</strong>
                <pre style="background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0;">cd backend
npm install express mysql2 bcryptjs jsonwebtoken cors dotenv</pre>
            </li>
            <li><strong>Configurer la base de données :</strong>
                <ul>
                    <li>Créer la base de données MySQL "GestionProjets"</li>
                    <li>Exécuter le script database.sql</li>
                    <li>Modifier le fichier .env avec vos paramètres MySQL</li>
                </ul>
            </li>
            <li><strong>Créer l\'utilisateur admin :</strong>
                <pre style="background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0;">node init-db.js</pre>
            </li>
            <li><strong>Démarrer le serveur backend :</strong>
                <pre style="background: #e9ecef; padding: 10px; border-radius: 4px; margin: 10px 0;">node server.js</pre>
            </li>
        </ol>
        
        <p style="margin-top: 20px; padding: 15px; background: #d1ecf1; border-radius: 4px; border-left: 4px solid #bee5eb;">
            <strong>💡 Astuce :</strong> Une fois le backend démarré, rechargez cette page pour accéder à l\'application.
        </p>
        
        <p style="margin-top: 15px;">
            <strong>Connexion par défaut :</strong><br>
            Nom d\'utilisateur: <code>admin</code><br>
            Mot de passe: <code>admin123</code>
        </p>
    </div>';
}
?>
