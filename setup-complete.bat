@echo off
echo ========================================
echo   ACTIVATION CONNEXION COMPLETE
echo   Systeme de Gestion de Projets
echo ========================================
echo.

echo [1/6] Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installe!
    echo 📥 Veuillez installer Node.js depuis: https://nodejs.org/
    echo 🔄 Redemarrez ce script apres l'installation
    pause
    exit /b 1
) else (
    echo ✅ Node.js detecte
)

echo.
echo [2/6] Installation des dependances backend...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de l'installation des dependances
    pause
    exit /b 1
)
cd ..
echo ✅ Dependances backend installees

echo.
echo [3/6] Installation des dependances frontend...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Erreur lors de l'installation des dependances frontend
    pause
    exit /b 1
)
echo ✅ Dependances frontend installees

echo.
echo [4/6] Verification de MySQL...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  MySQL non detecte automatiquement
    echo 📋 Assurez-vous que MySQL est installe et accessible
    echo 🔧 Configurez le fichier .env avec vos parametres MySQL
) else (
    echo ✅ MySQL detecte
)

echo.
echo [5/6] Creation de la base de donnees...
echo 📋 Executez manuellement dans MySQL:
echo    mysql -u root -p ^< database.sql
echo    cd backend ^&^& npm run init-db
echo.

echo [6/6] Demarrage des services...
echo 🚀 Pour demarrer le systeme:
echo    1. Backend: cd backend ^&^& npm start
echo    2. Frontend: npm run dev
echo.

echo ========================================
echo ✅ CONFIGURATION TERMINEE!
echo ========================================
echo.
echo 📋 PROCHAINES ETAPES:
echo 1. Configurez MySQL et executez database.sql
echo 2. Creez l'utilisateur admin: cd backend ^&^& npm run init-db
echo 3. Demarrez le backend: cd backend ^&^& npm start
echo 4. Demarrez le frontend: npm run dev
echo 5. Ouvrez http://localhost:5173 dans votre navigateur
echo.
echo 🔐 CONNEXION PAR DEFAUT:
echo    Utilisateur: admin
echo    Mot de passe: admin123
echo.
pause
