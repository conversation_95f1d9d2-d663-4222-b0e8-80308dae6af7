@echo off
echo ========================================
echo   Système de Gestion de Projets
echo ========================================
echo.

echo Vérification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Node.js n'est pas installé ou n'est pas dans le PATH
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js détecté: 
node --version

echo.
echo Vérification de MySQL...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ATTENTION: MySQL n'est pas détecté dans le PATH
    echo Assurez-vous que MySQL est installé et configuré
)

echo.
echo Installation des dépendances...
echo.

echo Installation des dépendances frontend...
call npm install
if %errorlevel% neq 0 (
    echo ERREUR: Échec de l'installation des dépendances frontend
    pause
    exit /b 1
)

echo.
echo Installation des dépendances backend...
cd backend
call npm install express mysql2 bcryptjs jsonwebtoken cors dotenv
if %errorlevel% neq 0 (
    echo ERREUR: Échec de l'installation des dépendances backend
    pause
    exit /b 1
)
cd ..

echo.
echo ========================================
echo   Installation terminée avec succès!
echo ========================================
echo.
echo PROCHAINES ÉTAPES:
echo.
echo 1. Configurez votre base de données MySQL
echo 2. Exécutez le script database.sql dans votre base de données
echo 3. Modifiez le fichier .env avec vos paramètres de base de données
echo 4. Exécutez 'node backend/init-db.js' pour créer l'utilisateur admin
echo 5. Démarrez l'application avec 'npm run dev' et 'node backend/server.js'
echo.
echo Utilisateur admin par défaut:
echo   Nom d'utilisateur: admin
echo   Mot de passe: admin123
echo.
pause
