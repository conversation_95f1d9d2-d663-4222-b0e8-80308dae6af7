const mysql = require('mysql2/promise')
require('dotenv').config()

async function testConnection() {
  console.log('🔍 Test de connexion à la base de données...')
  console.log('=====================================')
  
  try {
    // Configuration de la base de données
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'GestionProjets'
    }
    
    console.log('📋 Configuration:')
    console.log(`   Host: ${dbConfig.host}`)
    console.log(`   User: ${dbConfig.user}`)
    console.log(`   Database: ${dbConfig.database}`)
    console.log(`   Password: ${dbConfig.password ? '***' : '(vide)'}`)
    console.log('')
    
    // Test de connexion
    console.log('🔌 Tentative de connexion...')
    const connection = await mysql.createConnection(dbConfig)
    console.log('✅ Connexion réussie!')
    
    // Test des tables
    console.log('📊 Vérification des tables...')
    const [tables] = await connection.execute('SHOW TABLES')
    
    if (tables.length === 0) {
      console.log('⚠️  Aucune table trouvée!')
      console.log('💡 Exécutez: mysql -u root -p GestionProjets < database.sql')
    } else {
      console.log(`✅ ${tables.length} tables trouvées:`)
      tables.forEach(table => {
        console.log(`   - ${Object.values(table)[0]}`)
      })
    }
    
    // Test de l'utilisateur admin
    console.log('')
    console.log('👤 Vérification de l\'utilisateur admin...')
    try {
      const [users] = await connection.execute(
        'SELECT nom_utilisateur, nom_complet, est_super_admin FROM Utilisateurs WHERE nom_utilisateur = ?',
        ['admin']
      )
      
      if (users.length === 0) {
        console.log('⚠️  Utilisateur admin non trouvé!')
        console.log('💡 Exécutez: npm run init-db')
      } else {
        console.log('✅ Utilisateur admin trouvé:')
        console.log(`   - Nom: ${users[0].nom_complet}`)
        console.log(`   - Super Admin: ${users[0].est_super_admin ? 'Oui' : 'Non'}`)
      }
    } catch (error) {
      console.log('⚠️  Table Utilisateurs non trouvée')
    }
    
    await connection.end()
    
    console.log('')
    console.log('=====================================')
    console.log('✅ TEST DE CONNEXION TERMINÉ!')
    console.log('🚀 Vous pouvez maintenant démarrer le serveur')
    
  } catch (error) {
    console.log('')
    console.log('=====================================')
    console.log('❌ ERREUR DE CONNEXION!')
    console.log('=====================================')
    console.log('Détails de l\'erreur:')
    console.log(error.message)
    console.log('')
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('🔧 Solutions possibles:')
      console.log('1. Vérifiez le mot de passe MySQL')
      console.log('2. Modifiez DB_PASSWORD dans le fichier .env')
      console.log('3. Créez un utilisateur MySQL avec les bons droits')
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('🔧 Solutions possibles:')
      console.log('1. Créez la base de données: CREATE DATABASE GestionProjets;')
      console.log('2. Importez le schéma: mysql -u root -p GestionProjets < database.sql')
    } else if (error.code === 'ECONNREFUSED') {
      console.log('🔧 Solutions possibles:')
      console.log('1. Démarrez le service MySQL')
      console.log('2. Vérifiez que MySQL écoute sur le port 3306')
      console.log('3. Installez MySQL si ce n\'est pas fait')
    }
    
    console.log('')
    console.log('📖 Consultez GUIDE-MYSQL.md pour plus d\'aide')
  }
}

testConnection()
