<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Gestion de Projets</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            color: #333;
            position: relative;
            overflow-x: hidden;
        }

        /* Carrousel d'arrière-plan */
        .background-carousel {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
        }

        .background-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            opacity: 0;
            transition: opacity 1.5s ease-in-out;
        }

        .background-slide.active {
            opacity: 1;
        }

        /* Overlay pour améliorer la lisibilité */
        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(220, 38, 38, 0.7) 0%, rgba(185, 28, 28, 0.6) 50%, rgba(102, 126, 234, 0.5) 100%);
            z-index: -1;
        }

        /* En-tête avec logo */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 3px solid #dc2626;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .logo {
            height: 70px;
            width: auto;
            object-fit: contain;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .header-title {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }

        .header-title h1 {
            color: #dc2626;
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .header-title p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 1rem;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2);
        }

        .header-btn:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .header-btn.logout {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.2);
        }

        .header-btn.logout:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .logo {
            height: 60px;
            width: auto;
            object-fit: contain;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .header-title {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }

        .header-title h1 {
            color: #dc2626;
            margin: 0;
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .header-title p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .main-card {
            background: rgba(255, 255, 255, 0.92);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .main-card h2 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .main-card p {
            color: #718096;
            font-size: 1.2em;
        }

        /* Indicateur de carrousel */
        .carousel-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 10px 15px;
            border-radius: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: #666;
        }

        .carousel-dots {
            display: flex;
            gap: 5px;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ccc;
            transition: all 0.3s ease;
        }

        .carousel-dot.active {
            background: #dc2626;
            transform: scale(1.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.88);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card.blue {
            border-left: 5px solid #3182ce;
            background: linear-gradient(135deg, rgba(49, 130, 206, 0.1), rgba(49, 130, 206, 0.05));
        }

        .stat-card.green {
            border-left: 5px solid #22c55e;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
            box-shadow: 0 8px 32px rgba(34, 197, 94, 0.15);
        }

        .stat-card.orange {
            border-left: 5px solid #dd6b20;
            background: linear-gradient(135deg, rgba(221, 107, 32, 0.1), rgba(221, 107, 32, 0.05));
        }

        .stat-card.red {
            border-left: 5px solid #ef4444;
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
            box-shadow: 0 8px 32px rgba(239, 68, 68, 0.15);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-card.blue .stat-number { color: #3182ce; }
        .stat-card.green .stat-number { color: #22c55e; font-weight: 900; }
        .stat-card.orange .stat-number { color: #dd6b20; }
        .stat-card.red .stat-number { color: #ef4444; font-weight: 900; }

        .stat-label {
            color: #4a5568;
            font-size: 1.1em;
            font-weight: 600;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.88);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .card h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .project-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #4299e1;
            transition: all 0.3s ease;
        }

        .project-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .project-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .project-details {
            color: #718096;
            font-size: 0.9em;
        }

        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
        }

        .btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
        }

        .btn.green {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .btn.green:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
            box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
        }

        .btn.red {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .btn.red:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
        }

        .btn.orange {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
        }

        .btn.orange:hover {
            background: linear-gradient(135deg, #dd6b20, #c05621);
            box-shadow: 0 6px 20px rgba(237, 137, 54, 0.4);
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.88);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-card.blue .feature-icon { color: #3182ce; }
        .feature-card.green .feature-icon { color: #38a169; }
        .feature-card.orange .feature-icon { color: #dd6b20; }
        .feature-card.purple .feature-icon { color: #805ad5; }

        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }

        .feature-description {
            color: #718096;
            line-height: 1.6;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            white-space: nowrap;
            max-width: 200px;
            text-align: center;
        }

        .status-indicator.offline {
            background: #f56565;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .container {
                padding: 10px;
            }

            .status-indicator {
                top: 10px;
                right: 10px;
                padding: 6px 12px;
                font-size: 0.8em;
                max-width: 150px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .alert.success {
            background: #c6f6d5;
            border: 1px solid #9ae6b4;
            color: #2f855a;
        }

        .alert.info {
            background: #bee3f8;
            border: 1px solid #90cdf4;
            color: #2c5282;
        }
        /* Page de connexion */
        .login-container {
            display: none;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container.active {
            display: flex;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        .login-logo {
            height: 80px;
            width: auto;
            margin-bottom: 20px;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .login-title {
            color: #dc2626;
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-label {
            display: block;
            color: #374151;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-input:focus {
            outline: none;
            border-color: #dc2626;
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 14px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
        }

        .login-btn:disabled {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            opacity: 0.8;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        .error-message {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            border: 2px solid #ef4444;
            color: #dc2626;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
            animation: shake 0.5s ease-in-out;
        }

        .success-message {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            border: 2px solid #22c55e;
            color: #16a34a;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
            animation: slideIn 0.5s ease-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes slideIn {
            0% { transform: translateY(-10px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        .default-credentials {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.85rem;
            text-align: left;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-left: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        /* Dashboard principal */
        .main-dashboard {
            display: block;
        }

        .main-dashboard.hidden {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Carrousel d'arrière-plan -->
    <div class="background-carousel" id="backgroundCarousel">
        <div class="background-slide active" style="background-image: url('image.jpg/IMG_9331-504x300.jpg')"></div>
        <div class="background-slide" style="background-image: url('image.jpg/download.jpg')"></div>
        <div class="background-slide" style="background-image: url('image.jpg/images.jpg')"></div>
    </div>
    <div class="background-overlay"></div>

    <!-- Page de connexion -->
    <div class="login-container active" id="loginContainer">
        <div class="login-card">
            <img src="image.jpg/En-tete-36.png" alt="Logo Province de Khénifra" class="login-logo">
            <h1 class="login-title">Système de Gestion de Projets</h1>
            <p class="login-subtitle">Province de Khénifra</p>

            <form id="loginForm">
                <div id="loginError" class="error-message hidden"></div>
                <div id="loginSuccess" class="success-message hidden"></div>

                <div class="form-group">
                    <label for="username" class="form-label">👤 Nom d'utilisateur</label>
                    <input type="text" id="username" name="username" class="form-input" required>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">🔒 Mot de passe</label>
                    <input type="password" id="password" name="password" class="form-input" required>
                </div>

                <button type="submit" id="loginBtn" class="login-btn">
                    <span id="loginBtnText">Se connecter</span>
                    <span id="loginSpinner" class="loading-spinner hidden"></span>
                </button>

                <div class="default-credentials">
                    <strong>🔑 Connexion par défaut :</strong><br>
                    • <strong>Super Admin:</strong> admin / admin123<br>
                    • <strong>Admin Préfecture:</strong> admin_khenifra / demo123<br>
                    • <strong>Admin Commune:</strong> admin_commune / demo123
                </div>
            </form>
        </div>
    </div>

    <!-- Dashboard principal -->
    <div class="main-dashboard hidden" id="mainDashboard">
        <!-- En-tête avec logo -->
        <div class="header">
            <div class="header-content">
                <img src="image.jpg/En-tete-36.png" alt="Logo Province de Khénifra" class="logo">
                <div class="header-title">
                    <h1>Système de Gestion de Projets</h1>
                    <p>Province de Khénifra - Région Béni Mellal-Khénifra</p>
                </div>
                <div class="header-actions">
                    <span id="userInfo" class="header-btn">👤 Utilisateur</span>
                    <button onclick="logout()" class="header-btn logout">🚪 Déconnexion</button>
                </div>
            </div>
        </div>

        <div class="status-indicator" id="statusIndicator">
            🟢 En ligne
        </div>

        <div class="container">

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card blue">
                <div class="stat-number" id="totalProjects">25</div>
                <div class="stat-label">📁 Total des Projets</div>
            </div>
            <div class="stat-card green">
                <div class="stat-number" id="activeProjects">12</div>
                <div class="stat-label">🚀 Projets Actifs</div>
            </div>
            <div class="stat-card orange">
                <div class="stat-number" id="completedProjects">8</div>
                <div class="stat-label">✅ Projets Terminés</div>
            </div>
            <div class="stat-card red">
                <div class="stat-number" id="totalBudget">45.2M MAD</div>
                <div class="stat-label">💰 Budget Total</div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <!-- Projets récents -->
            <div class="card">
                <h2>📋 Projets Récents</h2>
                <div id="recentProjects">
                    <div class="project-item">
                        <div class="project-title">Construction Centre de Santé Rural</div>
                        <div class="project-details">Programme: Développement Rural • Budget: 2,500,000 MAD • Statut: En cours</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Réhabilitation École Primaire Moulay Hassan</div>
                        <div class="project-details">Programme: Éducation • Budget: 1,800,000 MAD • Statut: En cours</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Électrification Rurale - Douar Ait Brahim</div>
                        <div class="project-details">Programme: Infrastructure • Budget: 3,200,000 MAD • Statut: Terminé</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Adduction d'eau potable - Village Tafraout</div>
                        <div class="project-details">Programme: Eau et Assainissement • Budget: 4,500,000 MAD • Statut: En cours</div>
                    </div>
                </div>
            </div>

            <!-- Informations système -->
            <div class="card">
                <h2>ℹ️ Informations Système</h2>
                <div id="systemInfo">
                    <div class="alert info">
                        <strong>🟢 État du système:</strong> Opérationnel<br>
                        <strong>🗄️ Base de données:</strong> <span id="dbStatus">MySQL - Connectée ✅</span><br>
                        <strong>🔄 Dernière mise à jour:</strong> <span id="lastUpdate">-</span><br>
                        <strong>👥 Utilisateurs connectés:</strong> <span id="activeUsers">3</span><br>
                        <strong>📊 Version:</strong> v1.0.0
                    </div>

                    <div class="alert success" style="margin-top: 15px;">
                        <strong>✅ Système prêt!</strong><br>
                        Toutes les fonctionnalités sont opérationnelles.
                    </div>
                </div>
            </div>
        </div>

        <!-- Fonctionnalités -->
        <div class="feature-grid">
            <div class="feature-card blue">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Tableau de Bord</div>
                <div class="feature-description">Vue d'ensemble des projets avec statistiques en temps réel et indicateurs de performance</div>
            </div>
            <div class="feature-card green">
                <div class="feature-icon">🏗️</div>
                <div class="feature-title">Gestion de Projets</div>
                <div class="feature-description">Création, modification et suivi complet des projets avec localisation et budgets</div>
            </div>
            <div class="feature-card orange">
                <div class="feature-icon">👥</div>
                <div class="feature-title">Gestion Utilisateurs</div>
                <div class="feature-description">Contrôle d'accès basé sur les rôles et localisations géographiques</div>
            </div>
            <div class="feature-card purple">
                <div class="feature-icon">📈</div>
                <div class="feature-title">Rapports & Analytics</div>
                <div class="feature-description">Génération de rapports détaillés et analyses de performance des projets</div>
            </div>
        </div>

        <!-- Actions -->
        <div class="actions">
            <a href="#" class="btn green" onclick="showLogin()">🔐 Se Connecter</a>
            <a href="#" class="btn" onclick="showProjects()">📁 Voir les Projets</a>
            <a href="#" class="btn red" onclick="showDocumentation()">📚 Documentation</a>
        </div>

        <!-- Instructions -->
        <div class="card" style="margin-top: 30px;">
            <h2>🚀 Guide de Démarrage Rapide</h2>
            <div class="alert info">
                <strong>🔑 Connexion par défaut:</strong><br>
                • Nom d'utilisateur: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px;">admin</code><br>
                • Mot de passe: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px;">admin123</code><br><br>

                <strong>📋 Étapes de configuration:</strong><br>
                1. ✅ Configurez votre base de données MySQL<br>
                2. ✅ Exécutez le script <code>database.sql</code><br>
                3. ✅ Démarrez le serveur backend Node.js<br>
                4. ✅ Connectez-vous à l'application<br>
                5. ✅ Créez vos premiers projets
            </div>

            <div class="alert success" style="margin-top: 15px;">
                <strong>🎉 Félicitations!</strong><br>
                Votre système de gestion de projets est maintenant opérationnel. Vous pouvez commencer à gérer vos projets de développement régional.
            </div>
        </div>
    </div>

    <script>
        // Configuration API
        const API_BASE_URL = 'http://localhost:5000/api';
        let authToken = null;
        let currentUser = null;
        let projects = [];

        // Projets de démonstration
        const demoProjects = [
            {
                id: 1,
                intitule_projet: "Construction Centre de Santé Rural",
                programme: "Développement Rural",
                annee: 2024,
                secteur: "Santé",
                objectifs: "Améliorer l'accès aux soins de santé dans les zones rurales",
                consistance: "Construction d'un centre de santé de 500m² avec équipements médicaux",
                nombre_beneficiaires: 5000,
                cout_global: 2500000,
                statut: "En cours",
                commune: "Khénifra Centre",
                date_creation: "2024-01-15"
            },
            {
                id: 2,
                intitule_projet: "Réhabilitation École Primaire Moulay Hassan",
                programme: "Éducation",
                annee: 2024,
                secteur: "Éducation",
                objectifs: "Améliorer les conditions d'apprentissage des élèves",
                consistance: "Rénovation complète de l'école avec 8 salles de classe",
                nombre_beneficiaires: 300,
                cout_global: 1800000,
                statut: "En cours",
                commune: "Moulay Bouazza",
                date_creation: "2024-02-10"
            },
            {
                id: 3,
                intitule_projet: "Électrification Rurale - Douar Ait Brahim",
                programme: "Infrastructure",
                annee: 2023,
                secteur: "Énergie",
                objectifs: "Raccorder le douar au réseau électrique national",
                consistance: "Installation de 15 km de lignes électriques et transformateurs",
                nombre_beneficiaires: 800,
                cout_global: 3200000,
                statut: "Terminé",
                commune: "Ait Ishaq",
                date_creation: "2023-03-20"
            },
            {
                id: 4,
                intitule_projet: "Adduction d'eau potable - Village Tafraout",
                programme: "Eau et Assainissement",
                annee: 2024,
                secteur: "Hydraulique",
                objectifs: "Assurer l'accès à l'eau potable pour tous les habitants",
                consistance: "Forage de puits et installation de réseau de distribution",
                nombre_beneficiaires: 1200,
                cout_global: 4500000,
                statut: "En cours",
                commune: "Tafraout",
                date_creation: "2024-01-05"
            },
            {
                id: 5,
                intitule_projet: "Aménagement Route Rurale RR305",
                programme: "Infrastructure",
                annee: 2024,
                secteur: "Transport",
                objectifs: "Faciliter l'accès aux zones rurales enclavées",
                consistance: "Bitumage de 25 km de route rurale avec ouvrages d'art",
                nombre_beneficiaires: 2500,
                cout_global: 8500000,
                statut: "Planifié",
                commune: "Kerrouchen",
                date_creation: "2024-03-01"
            }
        ];

        // Carrousel d'images
        function initBackgroundCarousel() {
            const slides = document.querySelectorAll('.background-slide');
            let currentSlide = 0;

            function nextSlide() {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % slides.length;
                slides[currentSlide].classList.add('active');
            }

            // Changer d'image toutes les 3 secondes
            setInterval(nextSlide, 3000);
        }

        // Utilisateurs de démonstration
        const demoUsers = {
            'admin': {
                password: 'admin123',
                user: {
                    id_utilisateur: 1,
                    nom_utilisateur: 'admin',
                    nom_complet: 'Super Administrateur',
                    email: '<EMAIL>',
                    nom_role: 'Super Admin',
                    est_super_admin: true
                }
            },
            'admin_khenifra': {
                password: 'demo123',
                user: {
                    id_utilisateur: 2,
                    nom_utilisateur: 'admin_khenifra',
                    nom_complet: 'Administrateur Préfecture Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Préfecture',
                    nom_prefecture: 'Khénifra',
                    est_super_admin: false
                }
            },
            'admin_commune': {
                password: 'demo123',
                user: {
                    id_utilisateur: 3,
                    nom_utilisateur: 'admin_commune',
                    nom_complet: 'Administrateur Commune Khénifra',
                    email: '<EMAIL>',
                    nom_role: 'Admin Commune',
                    nom_commune: 'Commune Khénifra',
                    est_super_admin: false
                }
            }
        };

        // Gestion de l'authentification
        function login(event) {
            event.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            const loginSpinner = document.getElementById('loginSpinner');
            const loginError = document.getElementById('loginError');
            const loginSuccess = document.getElementById('loginSuccess');

            // Reset messages
            loginError.classList.add('hidden');
            loginSuccess.classList.add('hidden');

            // Loading state
            loginBtn.disabled = true;
            loginBtnText.textContent = 'Connexion...';
            loginSpinner.classList.remove('hidden');

            // Mode démonstration direct (pas de tentative backend)
            setTimeout(() => {
                console.log('🔍 Debug - Username:', username);
                console.log('🔍 Debug - Password:', password);
                console.log('🔍 Debug - DemoUsers:', demoUsers);

                const demoUser = demoUsers[username];
                console.log('🔍 Debug - DemoUser found:', demoUser);

                if (demoUser) {
                    console.log('🔍 Debug - Expected password:', demoUser.password);
                    console.log('🔍 Debug - Password match:', demoUser.password === password);
                }

                if (demoUser && demoUser.password === password) {
                    authToken = 'demo_token_' + Date.now();
                    currentUser = demoUser.user;
                    localStorage.setItem('authToken', authToken);
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));

                    // Afficher message de succès
                    loginSuccess.textContent = `Bienvenue ${currentUser.nom_complet} ! ✅`;
                    loginSuccess.classList.remove('hidden');

                    // Rediriger vers le dashboard après 1 seconde
                    setTimeout(() => {
                        showDashboard();
                    }, 1000);
                } else {
                    loginError.textContent = 'Nom d\'utilisateur ou mot de passe incorrect. Utilisez: admin/admin123, admin_khenifra/demo123, ou admin_commune/demo123';
                    loginError.classList.remove('hidden');
                }

                loginBtn.disabled = false;
                loginBtnText.textContent = 'Se connecter';
                loginSpinner.classList.add('hidden');
            }, 800);
        }

        function showDashboard() {
            document.getElementById('loginContainer').classList.remove('active');
            document.getElementById('mainDashboard').classList.remove('hidden');

            // Mettre à jour les informations utilisateur
            if (currentUser) {
                document.getElementById('userInfo').textContent = `👤 ${currentUser.nom_complet}`;

                // Personnaliser le dashboard selon le rôle
                updateDashboardForRole();

                // Ajouter les fonctionnalités admin si nécessaire
                if (currentUser.nom_role === 'Super Admin' || currentUser.est_super_admin) {
                    addAdminFeatures();
                }
            }

            // Démarrer les mises à jour du dashboard
            updateStats();
            checkSystemStatus();
            loadProjects();
        }

        function addAdminFeatures() {
            // Ajouter le bouton de gestion des projets
            const headerActions = document.querySelector('.header-actions');
            if (!document.getElementById('adminProjectsBtn')) {
                const adminBtn = document.createElement('button');
                adminBtn.id = 'adminProjectsBtn';
                adminBtn.className = 'header-btn';
                adminBtn.innerHTML = '🏗️ Gestion Projets';
                adminBtn.onclick = showProjectManagement;
                headerActions.insertBefore(adminBtn, headerActions.lastElementChild);
            }
        }

        function updateDashboardForRole() {
            const headerTitle = document.querySelector('.header-title h1');
            const headerSubtitle = document.querySelector('.header-title p');

            if (currentUser.nom_role === 'Super Admin') {
                headerTitle.textContent = '🏛️ Administration Générale - Province de Khénifra';
                headerSubtitle.textContent = 'Accès complet à tous les projets et fonctions administratives';
                updateStatsForSuperAdmin();
            } else if (currentUser.nom_role === 'Admin Préfecture') {
                headerTitle.textContent = '🏢 Administration Préfectorale - Khénifra';
                headerSubtitle.textContent = 'Gestion des projets de la Préfecture de Khénifra';
                updateStatsForPrefecture();
            } else if (currentUser.nom_role === 'Admin Commune') {
                headerTitle.textContent = '🏘️ Administration Communale - Khénifra';
                headerSubtitle.textContent = 'Gestion des projets de la Commune de Khénifra';
                updateStatsForCommune();
            }
        }

        function updateStatsForSuperAdmin() {
            document.getElementById('totalProjects').textContent = '45';
            document.getElementById('activeProjects').textContent = '28';
            document.getElementById('completedProjects').textContent = '17';
            document.getElementById('totalBudget').textContent = '125.8M MAD';
        }

        function updateStatsForPrefecture() {
            document.getElementById('totalProjects').textContent = '32';
            document.getElementById('activeProjects').textContent = '19';
            document.getElementById('completedProjects').textContent = '13';
            document.getElementById('totalBudget').textContent = '89.2M MAD';
        }

        function updateStatsForCommune() {
            document.getElementById('totalProjects').textContent = '18';
            document.getElementById('activeProjects').textContent = '11';
            document.getElementById('completedProjects').textContent = '7';
            document.getElementById('totalBudget').textContent = '34.5M MAD';
        }

        // Gestion des projets
        function loadProjects() {
            projects = [...demoProjects];
            updateProjectsList();
        }

        function updateProjectsList() {
            const projectsContainer = document.getElementById('recentProjects');
            if (!projectsContainer) return;

            projectsContainer.innerHTML = '';

            projects.slice(0, 4).forEach(project => {
                const projectDiv = document.createElement('div');
                projectDiv.className = 'project-item';
                projectDiv.innerHTML = `
                    <div class="project-title">${project.intitule_projet}</div>
                    <div class="project-details">
                        Programme: ${project.programme} •
                        Budget: ${formatCurrency(project.cout_global)} •
                        Statut: ${project.statut} •
                        Bénéficiaires: ${project.nombre_beneficiaires}
                    </div>
                `;
                projectsContainer.appendChild(projectDiv);
            });
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('fr-MA', {
                style: 'currency',
                currency: 'MAD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function showProjectManagement() {
            const modal = createModal('🏗️ Gestion des Projets - Administration', `
                <div style="padding: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #333;">Liste des Projets</h3>
                        <button onclick="showAddProjectForm()" class="btn green" style="margin: 0;">
                            ➕ Nouveau Projet
                        </button>
                    </div>

                    <div id="projectsTable" style="max-height: 400px; overflow-y: auto;">
                        ${generateProjectsTable()}
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn">Fermer</button>
                    </div>
                </div>
            `);
        }

        function generateProjectsTable() {
            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse; font-size: 0.9rem;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                            <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Projet</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Programme</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Budget</th>
                            <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Statut</th>
                            <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            projects.forEach(project => {
                const statusColor = project.statut === 'Terminé' ? '#22c55e' :
                                  project.statut === 'En cours' ? '#3b82f6' : '#f59e0b';

                tableHTML += `
                    <tr style="border-bottom: 1px solid #dee2e6;">
                        <td style="padding: 12px; border: 1px solid #dee2e6;">
                            <strong>${project.intitule_projet}</strong><br>
                            <small style="color: #666;">${project.commune}</small>
                        </td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">${project.programme}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">${formatCurrency(project.cout_global)}</td>
                        <td style="padding: 12px; border: 1px solid #dee2e6;">
                            <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8rem;">
                                ${project.statut}
                            </span>
                        </td>
                        <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">
                            <button onclick="viewProject(${project.id})" class="btn" style="margin: 2px; padding: 6px 12px; font-size: 0.8rem;">
                                👁️ Voir
                            </button>
                            <button onclick="editProject(${project.id})" class="btn green" style="margin: 2px; padding: 6px 12px; font-size: 0.8rem;">
                                ✏️ Modifier
                            </button>
                            <button onclick="deleteProject(${project.id})" class="btn red" style="margin: 2px; padding: 6px 12px; font-size: 0.8rem;">
                                🗑️ Supprimer
                            </button>
                        </td>
                    </tr>
                `;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            return tableHTML;
        }

        function showAddProjectForm() {
            const modal = createModal('➕ Nouveau Projet', `
                <form id="addProjectForm" style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Intitulé du Projet *</label>
                            <input type="text" id="intitule_projet" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Programme *</label>
                            <select id="programme" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">Sélectionner...</option>
                                <option value="Développement Rural">Développement Rural</option>
                                <option value="Éducation">Éducation</option>
                                <option value="Infrastructure">Infrastructure</option>
                                <option value="Eau et Assainissement">Eau et Assainissement</option>
                                <option value="Santé">Santé</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Secteur *</label>
                            <select id="secteur" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">Sélectionner...</option>
                                <option value="Santé">Santé</option>
                                <option value="Éducation">Éducation</option>
                                <option value="Transport">Transport</option>
                                <option value="Hydraulique">Hydraulique</option>
                                <option value="Énergie">Énergie</option>
                                <option value="Agriculture">Agriculture</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Commune *</label>
                            <select id="commune" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="">Sélectionner...</option>
                                <option value="Khénifra Centre">Khénifra Centre</option>
                                <option value="Moulay Bouazza">Moulay Bouazza</option>
                                <option value="Ait Ishaq">Ait Ishaq</option>
                                <option value="Tafraout">Tafraout</option>
                                <option value="Kerrouchen">Kerrouchen</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Année *</label>
                            <input type="number" id="annee" value="2024" min="2020" max="2030" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Statut *</label>
                            <select id="statut" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="Planifié">Planifié</option>
                                <option value="En cours">En cours</option>
                                <option value="Terminé">Terminé</option>
                                <option value="Suspendu">Suspendu</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Coût Global (MAD) *</label>
                            <input type="number" id="cout_global" min="0" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Nombre de Bénéficiaires *</label>
                            <input type="number" id="nombre_beneficiaires" min="0" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Objectifs *</label>
                        <textarea id="objectifs" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 60px;"></textarea>
                    </div>
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Consistance *</label>
                        <textarea id="consistance" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 60px;"></textarea>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn green">✅ Créer le Projet</button>
                        <button type="button" onclick="closeModal()" class="btn red">❌ Annuler</button>
                    </div>
                </form>
            `);

            document.getElementById('addProjectForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addProject();
            });
        }

        function addProject() {
            const newProject = {
                id: Math.max(...projects.map(p => p.id)) + 1,
                intitule_projet: document.getElementById('intitule_projet').value,
                programme: document.getElementById('programme').value,
                annee: parseInt(document.getElementById('annee').value),
                secteur: document.getElementById('secteur').value,
                objectifs: document.getElementById('objectifs').value,
                consistance: document.getElementById('consistance').value,
                nombre_beneficiaires: parseInt(document.getElementById('nombre_beneficiaires').value),
                cout_global: parseInt(document.getElementById('cout_global').value),
                statut: document.getElementById('statut').value,
                commune: document.getElementById('commune').value,
                date_creation: new Date().toISOString().split('T')[0]
            };

            projects.push(newProject);
            updateProjectsList();
            closeModal();
            showProjectManagement(); // Refresh the management view

            // Show success message
            showNotification('✅ Projet créé avec succès !', 'success');
        }

        function viewProject(id) {
            const project = projects.find(p => p.id === id);
            if (!project) return;

            const modal = createModal(`👁️ Détails du Projet: ${project.intitule_projet}`, `
                <div style="padding: 20px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <h4 style="color: #22c55e; margin-bottom: 10px;">📋 Informations Générales</h4>
                            <p><strong>Programme:</strong> ${project.programme}</p>
                            <p><strong>Secteur:</strong> ${project.secteur}</p>
                            <p><strong>Commune:</strong> ${project.commune}</p>
                            <p><strong>Année:</strong> ${project.annee}</p>
                            <p><strong>Statut:</strong> <span style="background: ${project.statut === 'Terminé' ? '#22c55e' : project.statut === 'En cours' ? '#3b82f6' : '#f59e0b'}; color: white; padding: 4px 8px; border-radius: 4px;">${project.statut}</span></p>
                        </div>
                        <div>
                            <h4 style="color: #ef4444; margin-bottom: 10px;">💰 Informations Financières</h4>
                            <p><strong>Coût Global:</strong> ${formatCurrency(project.cout_global)}</p>
                            <p><strong>Bénéficiaires:</strong> ${project.nombre_beneficiaires.toLocaleString()} personnes</p>
                            <p><strong>Date de Création:</strong> ${new Date(project.date_creation).toLocaleDateString('fr-FR')}</p>
                        </div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #3b82f6; margin-bottom: 10px;">🎯 Objectifs</h4>
                        <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #3b82f6;">${project.objectifs}</p>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: #8b5cf6; margin-bottom: 10px;">🔧 Consistance</h4>
                        <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #8b5cf6;">${project.consistance}</p>
                    </div>
                    <div style="text-align: center;">
                        <button onclick="editProject(${project.id})" class="btn green">✏️ Modifier</button>
                        <button onclick="deleteProject(${project.id})" class="btn red">🗑️ Supprimer</button>
                        <button onclick="closeModal()" class="btn">Fermer</button>
                    </div>
                </div>
            `);
        }

        function editProject(id) {
            const project = projects.find(p => p.id === id);
            if (!project) return;

            const modal = createModal(`✏️ Modifier le Projet: ${project.intitule_projet}`, `
                <form id="editProjectForm" style="padding: 20px;">
                    <input type="hidden" id="edit_project_id" value="${project.id}">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Intitulé du Projet *</label>
                            <input type="text" id="edit_intitule_projet" value="${project.intitule_projet}" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Programme *</label>
                            <select id="edit_programme" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="Développement Rural" ${project.programme === 'Développement Rural' ? 'selected' : ''}>Développement Rural</option>
                                <option value="Éducation" ${project.programme === 'Éducation' ? 'selected' : ''}>Éducation</option>
                                <option value="Infrastructure" ${project.programme === 'Infrastructure' ? 'selected' : ''}>Infrastructure</option>
                                <option value="Eau et Assainissement" ${project.programme === 'Eau et Assainissement' ? 'selected' : ''}>Eau et Assainissement</option>
                                <option value="Santé" ${project.programme === 'Santé' ? 'selected' : ''}>Santé</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Secteur *</label>
                            <select id="edit_secteur" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="Santé" ${project.secteur === 'Santé' ? 'selected' : ''}>Santé</option>
                                <option value="Éducation" ${project.secteur === 'Éducation' ? 'selected' : ''}>Éducation</option>
                                <option value="Transport" ${project.secteur === 'Transport' ? 'selected' : ''}>Transport</option>
                                <option value="Hydraulique" ${project.secteur === 'Hydraulique' ? 'selected' : ''}>Hydraulique</option>
                                <option value="Énergie" ${project.secteur === 'Énergie' ? 'selected' : ''}>Énergie</option>
                                <option value="Agriculture" ${project.secteur === 'Agriculture' ? 'selected' : ''}>Agriculture</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Commune *</label>
                            <select id="edit_commune" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="Khénifra Centre" ${project.commune === 'Khénifra Centre' ? 'selected' : ''}>Khénifra Centre</option>
                                <option value="Moulay Bouazza" ${project.commune === 'Moulay Bouazza' ? 'selected' : ''}>Moulay Bouazza</option>
                                <option value="Ait Ishaq" ${project.commune === 'Ait Ishaq' ? 'selected' : ''}>Ait Ishaq</option>
                                <option value="Tafraout" ${project.commune === 'Tafraout' ? 'selected' : ''}>Tafraout</option>
                                <option value="Kerrouchen" ${project.commune === 'Kerrouchen' ? 'selected' : ''}>Kerrouchen</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Année *</label>
                            <input type="number" id="edit_annee" value="${project.annee}" min="2020" max="2030" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Statut *</label>
                            <select id="edit_statut" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="Planifié" ${project.statut === 'Planifié' ? 'selected' : ''}>Planifié</option>
                                <option value="En cours" ${project.statut === 'En cours' ? 'selected' : ''}>En cours</option>
                                <option value="Terminé" ${project.statut === 'Terminé' ? 'selected' : ''}>Terminé</option>
                                <option value="Suspendu" ${project.statut === 'Suspendu' ? 'selected' : ''}>Suspendu</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Coût Global (MAD) *</label>
                            <input type="number" id="edit_cout_global" value="${project.cout_global}" min="0" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Nombre de Bénéficiaires *</label>
                            <input type="number" id="edit_nombre_beneficiaires" value="${project.nombre_beneficiaires}" min="0" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Objectifs *</label>
                        <textarea id="edit_objectifs" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 60px;">${project.objectifs}</textarea>
                    </div>
                    <div style="margin-top: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">Consistance *</label>
                        <textarea id="edit_consistance" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 60px;">${project.consistance}</textarea>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn green">✅ Sauvegarder</button>
                        <button type="button" onclick="closeModal()" class="btn red">❌ Annuler</button>
                    </div>
                </form>
            `);

            document.getElementById('editProjectForm').addEventListener('submit', function(e) {
                e.preventDefault();
                updateProject();
            });
        }

        function updateProject() {
            const id = parseInt(document.getElementById('edit_project_id').value);
            const projectIndex = projects.findIndex(p => p.id === id);

            if (projectIndex !== -1) {
                projects[projectIndex] = {
                    ...projects[projectIndex],
                    intitule_projet: document.getElementById('edit_intitule_projet').value,
                    programme: document.getElementById('edit_programme').value,
                    annee: parseInt(document.getElementById('edit_annee').value),
                    secteur: document.getElementById('edit_secteur').value,
                    objectifs: document.getElementById('edit_objectifs').value,
                    consistance: document.getElementById('edit_consistance').value,
                    nombre_beneficiaires: parseInt(document.getElementById('edit_nombre_beneficiaires').value),
                    cout_global: parseInt(document.getElementById('edit_cout_global').value),
                    statut: document.getElementById('edit_statut').value,
                    commune: document.getElementById('edit_commune').value
                };

                updateProjectsList();
                closeModal();
                showProjectManagement(); // Refresh the management view

                // Show success message
                showNotification('✅ Projet modifié avec succès !', 'success');
            }
        }

        function deleteProject(id) {
            const project = projects.find(p => p.id === id);
            if (!project) return;

            if (confirm(`⚠️ Êtes-vous sûr de vouloir supprimer le projet "${project.intitule_projet}" ?\n\nCette action est irréversible.`)) {
                projects = projects.filter(p => p.id !== id);
                updateProjectsList();
                closeModal();
                showProjectManagement(); // Refresh the management view

                // Show success message
                showNotification('🗑️ Projet supprimé avec succès !', 'success');
            }
        }

        function logout() {
            authToken = null;
            currentUser = null;
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');

            document.getElementById('mainDashboard').classList.add('hidden');
            document.getElementById('loginContainer').classList.add('active');

            // Reset form
            document.getElementById('loginForm').reset();
        }

        // Vérifier si l'utilisateur est déjà connecté
        function checkAuthStatus() {
            const savedToken = localStorage.getItem('authToken');
            const savedUser = localStorage.getItem('currentUser');

            if (savedToken && savedUser) {
                authToken = savedToken;
                currentUser = JSON.parse(savedUser);
                showDashboard();
            }
        }

        // Fonctions utilitaires
        function createModal(title, content) {
            // Supprimer toute modale existante
            const existingModal = document.getElementById('modal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                backdrop-filter: blur(5px);
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 15px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                animation: modalSlideIn 0.3s ease-out;
            `;

            modalContent.innerHTML = `
                <div style="background: linear-gradient(135deg, #22c55e, #16a34a); color: white; padding: 20px; border-radius: 15px 15px 0 0;">
                    <h2 style="margin: 0; font-size: 1.5rem;">${title}</h2>
                    <button onclick="closeModal()" style="position: absolute; top: 15px; right: 20px; background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.5rem; cursor: pointer; border-radius: 50%; width: 35px; height: 35px; display: flex; align-items: center; justify-content: center;">×</button>
                </div>
                <div>${content}</div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Fermer en cliquant à l'extérieur
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Ajouter les styles d'animation
            if (!document.getElementById('modalStyles')) {
                const style = document.createElement('style');
                style.id = 'modalStyles';
                style.textContent = `
                    @keyframes modalSlideIn {
                        from { transform: translateY(-50px); opacity: 0; }
                        to { transform: translateY(0); opacity: 1; }
                    }
                `;
                document.head.appendChild(style);
            }

            return modal;
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.style.animation = 'modalSlideOut 0.3s ease-in';
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #22c55e, #16a34a)' :
                            type === 'error' ? 'linear-gradient(135deg, #ef4444, #dc2626)' :
                            'linear-gradient(135deg, #3b82f6, #2563eb)'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                z-index: 10001;
                font-weight: 600;
                animation: notificationSlideIn 0.3s ease-out;
                max-width: 300px;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Supprimer après 3 secondes
            setTimeout(() => {
                notification.style.animation = 'notificationSlideOut 0.3s ease-in';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);

            // Ajouter les styles d'animation pour les notifications
            if (!document.getElementById('notificationStyles')) {
                const style = document.createElement('style');
                style.id = 'notificationStyles';
                style.textContent = `
                    @keyframes notificationSlideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes notificationSlideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // Simulation de données pour la démonstration
        function updateStats() {
            const stats = [
                { id: 'totalProjects', value: Math.floor(Math.random() * 10) + 20 },
                { id: 'activeProjects', value: Math.floor(Math.random() * 8) + 8 },
                { id: 'completedProjects', value: Math.floor(Math.random() * 5) + 5 },
                { id: 'totalBudget', value: (Math.random() * 20 + 30).toFixed(1) + 'M MAD' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    element.textContent = stat.value;
                    element.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        element.style.transform = 'scale(1)';
                    }, 200);
                }
            });

            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('fr-FR');
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 5) + 1;
        }

        function checkSystemStatus() {
            // Simulation de vérification du système
            const statusIndicator = document.getElementById('statusIndicator');
            const dbStatus = document.getElementById('dbStatus');

            // Animation de vérification
            statusIndicator.innerHTML = '🔄 Vérification...';
            statusIndicator.style.background = '#ed8936';

            setTimeout(() => {
                statusIndicator.innerHTML = '🟢 Opérationnel';
                statusIndicator.style.background = '#48bb78';
                dbStatus.innerHTML = 'MySQL - Connectée ✅';
            }, 1500);
        }

        function showLogin() {
            const modal = createModal('🔐 Connexion au Système', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Interface de connexion disponible !</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>🔑 Comptes par défaut :</strong><br>
                        • <strong>Super Admin:</strong> admin / admin123<br>
                        • <strong>Admin Préfecture:</strong> admin_khenifra / demo123<br>
                        • <strong>Admin Commune:</strong> admin_commune_khenifra / demo123
                    </div>

                    <p style="margin: 15px 0;">Pour activer la connexion complète :</p>
                    <ol style="margin-left: 20px; line-height: 1.6;">
                        <li>Configurez MySQL et exécutez database.sql</li>
                        <li>Démarrez le serveur backend Node.js</li>
                        <li>Utilisez l'interface de connexion</li>
                    </ol>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn">Compris</button>
                    </div>
                </div>
            `);
        }

        function showProjects() {
            const modal = createModal('📁 Gestion des Projets', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Module de gestion des projets</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>✨ Fonctionnalités disponibles :</strong><br>
                        • Création et modification de projets<br>
                        • Suivi des budgets et bénéficiaires<br>
                        • Localisation par commune<br>
                        • Gestion des marchés et financements<br>
                        • Rapports et statistiques
                    </div>

                    <div style="background: #e6fffa; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #38a169;">
                        <strong>🚀 Projets d'exemple :</strong><br>
                        • Construction Centre de Santé Rural<br>
                        • Réhabilitation École Primaire<br>
                        • Électrification Rurale<br>
                        • Adduction d'eau potable
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn green">Parfait</button>
                    </div>
                </div>
            `);
        }

        function showDocumentation() {
            const modal = createModal('📚 Documentation', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Documentation complète du système</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>📖 Guides disponibles :</strong><br>
                        • README.md - Guide d'installation<br>
                        • database.sql - Structure de la base<br>
                        • demo-data.sql - Données d'exemple<br>
                        • Configuration backend et frontend
                    </div>

                    <div style="background: #fff5f5; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #f56565;">
                        <strong>⚠️ Prérequis :</strong><br>
                        • Node.js (version 16+)<br>
                        • MySQL (version 8.0+)<br>
                        • Serveur web (Apache/Nginx)
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn orange">Compris</button>
                    </div>
                </div>
            `);
        }

        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        background: linear-gradient(135deg, #4299e1, #3182ce);
                        color: white;
                        padding: 20px;
                        border-radius: 15px 15px 0 0;
                        text-align: center;
                        font-size: 1.2em;
                        font-weight: 600;
                    ">
                        ${title}
                    </div>
                    ${content}
                </div>
            `;

            modal.onclick = (e) => {
                if (e.target === modal) closeModal();
            };

            document.body.appendChild(modal);
            return modal;
        }

        function closeModal() {
            const modals = document.querySelectorAll('div[style*="position: fixed"]');
            modals.forEach(modal => {
                if (modal.style.zIndex === '1000') {
                    modal.remove();
                }
            });
        }

        // Animation des cartes au chargement
        function animateCards() {
            const cards = document.querySelectorAll('.stat-card, .card, .feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        }

        // Animation des projets
        function animateProjects() {
            const projects = document.querySelectorAll('.project-item');
            projects.forEach((project, index) => {
                project.style.opacity = '0';
                project.style.transform = 'translateX(-20px)';
                setTimeout(() => {
                    project.style.transition = 'all 0.6s ease';
                    project.style.opacity = '1';
                    project.style.transform = 'translateX(0)';
                }, 1000 + (index * 200));
            });
        }

        // Effet de typing pour le titre
        function typeTitle() {
            const title = document.querySelector('.header h1');
            const text = title.textContent;
            title.textContent = '';

            let i = 0;
            const typeInterval = setInterval(() => {
                title.textContent += text[i];
                i++;
                if (i >= text.length) {
                    clearInterval(typeInterval);
                }
            }, 100);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser le carrousel d'images
            initBackgroundCarousel();

            // Vérifier si l'utilisateur est déjà connecté
            checkAuthStatus();

            // Gestionnaires d'événements
            document.getElementById('loginForm').addEventListener('submit', login);

            // Démarrer les vérifications système
            setInterval(checkSystemStatus, 30000); // Vérifier toutes les 30 secondes

            // Ajouter des effets hover dynamiques
            addHoverEffects();
        });

        function addHoverEffects() {
            // Effet sur les cartes de statistiques
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Effet sur les boutons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // Effet de parallaxe simple
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('body');
            const speed = scrolled * 0.3;
            parallax.style.backgroundPosition = `center ${speed}px`;
        });

        // Gestion du clavier
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
