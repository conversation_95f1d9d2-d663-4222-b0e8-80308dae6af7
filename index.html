<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Gestion de Projets</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #718096;
            font-size: 1.2em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card.blue {
            border-left: 5px solid #3182ce;
        }

        .stat-card.green {
            border-left: 5px solid #38a169;
        }

        .stat-card.orange {
            border-left: 5px solid #dd6b20;
        }

        .stat-card.purple {
            border-left: 5px solid #805ad5;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-card.blue .stat-number { color: #3182ce; }
        .stat-card.green .stat-number { color: #38a169; }
        .stat-card.orange .stat-number { color: #dd6b20; }
        .stat-card.purple .stat-number { color: #805ad5; }

        .stat-label {
            color: #4a5568;
            font-size: 1.1em;
            font-weight: 600;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .project-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #4299e1;
            transition: all 0.3s ease;
        }

        .project-item:hover {
            background: #edf2f7;
            transform: translateX(5px);
        }

        .project-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .project-details {
            color: #718096;
            font-size: 0.9em;
        }

        .btn {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn.green {
            background: linear-gradient(135deg, #48bb78, #38a169);
        }

        .btn.green:hover {
            background: linear-gradient(135deg, #38a169, #2f855a);
        }

        .btn.orange {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
        }

        .btn.orange:hover {
            background: linear-gradient(135deg, #dd6b20, #c05621);
        }

        .actions {
            text-align: center;
            margin-top: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .feature-card.blue .feature-icon { color: #3182ce; }
        .feature-card.green .feature-icon { color: #38a169; }
        .feature-card.orange .feature-icon { color: #dd6b20; }
        .feature-card.purple .feature-icon { color: #805ad5; }

        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2d3748;
        }

        .feature-description {
            color: #718096;
            line-height: 1.6;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            white-space: nowrap;
            max-width: 200px;
            text-align: center;
        }

        .status-indicator.offline {
            background: #f56565;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .container {
                padding: 10px;
            }

            .status-indicator {
                top: 10px;
                right: 10px;
                padding: 6px 12px;
                font-size: 0.8em;
                max-width: 150px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .alert.success {
            background: #c6f6d5;
            border: 1px solid #9ae6b4;
            color: #2f855a;
        }

        .alert.info {
            background: #bee3f8;
            border: 1px solid #90cdf4;
            color: #2c5282;
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="statusIndicator">
        🟢 En ligne
    </div>

    <div class="container">
        <!-- En-tête -->
        <div class="header">
            <h1>🏗️ Système de Gestion de Projets</h1>
            <p>Plateforme de gestion des projets de développement régional</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid">
            <div class="stat-card blue">
                <div class="stat-number" id="totalProjects">25</div>
                <div class="stat-label">📁 Total des Projets</div>
            </div>
            <div class="stat-card green">
                <div class="stat-number" id="activeProjects">12</div>
                <div class="stat-label">🚀 Projets Actifs</div>
            </div>
            <div class="stat-card orange">
                <div class="stat-number" id="completedProjects">8</div>
                <div class="stat-label">✅ Projets Terminés</div>
            </div>
            <div class="stat-card purple">
                <div class="stat-number" id="totalBudget">45.2M MAD</div>
                <div class="stat-label">💰 Budget Total</div>
            </div>
        </div>

        <!-- Contenu principal -->
        <div class="main-content">
            <!-- Projets récents -->
            <div class="card">
                <h2>📋 Projets Récents</h2>
                <div id="recentProjects">
                    <div class="project-item">
                        <div class="project-title">Construction Centre de Santé Rural</div>
                        <div class="project-details">Programme: Développement Rural • Budget: 2,500,000 MAD • Statut: En cours</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Réhabilitation École Primaire Moulay Hassan</div>
                        <div class="project-details">Programme: Éducation • Budget: 1,800,000 MAD • Statut: En cours</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Électrification Rurale - Douar Ait Brahim</div>
                        <div class="project-details">Programme: Infrastructure • Budget: 3,200,000 MAD • Statut: Terminé</div>
                    </div>
                    <div class="project-item">
                        <div class="project-title">Adduction d'eau potable - Village Tafraout</div>
                        <div class="project-details">Programme: Eau et Assainissement • Budget: 4,500,000 MAD • Statut: En cours</div>
                    </div>
                </div>
            </div>

            <!-- Informations système -->
            <div class="card">
                <h2>ℹ️ Informations Système</h2>
                <div id="systemInfo">
                    <div class="alert info">
                        <strong>🟢 État du système:</strong> Opérationnel<br>
                        <strong>🗄️ Base de données:</strong> <span id="dbStatus">MySQL - Connectée ✅</span><br>
                        <strong>🔄 Dernière mise à jour:</strong> <span id="lastUpdate">-</span><br>
                        <strong>👥 Utilisateurs connectés:</strong> <span id="activeUsers">3</span><br>
                        <strong>📊 Version:</strong> v1.0.0
                    </div>

                    <div class="alert success" style="margin-top: 15px;">
                        <strong>✅ Système prêt!</strong><br>
                        Toutes les fonctionnalités sont opérationnelles.
                    </div>
                </div>
            </div>
        </div>

        <!-- Fonctionnalités -->
        <div class="feature-grid">
            <div class="feature-card blue">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Tableau de Bord</div>
                <div class="feature-description">Vue d'ensemble des projets avec statistiques en temps réel et indicateurs de performance</div>
            </div>
            <div class="feature-card green">
                <div class="feature-icon">🏗️</div>
                <div class="feature-title">Gestion de Projets</div>
                <div class="feature-description">Création, modification et suivi complet des projets avec localisation et budgets</div>
            </div>
            <div class="feature-card orange">
                <div class="feature-icon">👥</div>
                <div class="feature-title">Gestion Utilisateurs</div>
                <div class="feature-description">Contrôle d'accès basé sur les rôles et localisations géographiques</div>
            </div>
            <div class="feature-card purple">
                <div class="feature-icon">📈</div>
                <div class="feature-title">Rapports & Analytics</div>
                <div class="feature-description">Génération de rapports détaillés et analyses de performance des projets</div>
            </div>
        </div>

        <!-- Actions -->
        <div class="actions">
            <a href="#" class="btn" onclick="showLogin()">🔐 Se Connecter</a>
            <a href="#" class="btn green" onclick="showProjects()">📁 Voir les Projets</a>
            <a href="#" class="btn orange" onclick="showDocumentation()">📚 Documentation</a>
        </div>

        <!-- Instructions -->
        <div class="card" style="margin-top: 30px;">
            <h2>🚀 Guide de Démarrage Rapide</h2>
            <div class="alert info">
                <strong>🔑 Connexion par défaut:</strong><br>
                • Nom d'utilisateur: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px;">admin</code><br>
                • Mot de passe: <code style="background: #e2e8f0; padding: 2px 6px; border-radius: 4px;">admin123</code><br><br>

                <strong>📋 Étapes de configuration:</strong><br>
                1. ✅ Configurez votre base de données MySQL<br>
                2. ✅ Exécutez le script <code>database.sql</code><br>
                3. ✅ Démarrez le serveur backend Node.js<br>
                4. ✅ Connectez-vous à l'application<br>
                5. ✅ Créez vos premiers projets
            </div>

            <div class="alert success" style="margin-top: 15px;">
                <strong>🎉 Félicitations!</strong><br>
                Votre système de gestion de projets est maintenant opérationnel. Vous pouvez commencer à gérer vos projets de développement régional.
            </div>
        </div>
    </div>

    <script>
        // Simulation de données pour la démonstration
        function updateStats() {
            const stats = [
                { id: 'totalProjects', value: Math.floor(Math.random() * 10) + 20 },
                { id: 'activeProjects', value: Math.floor(Math.random() * 8) + 8 },
                { id: 'completedProjects', value: Math.floor(Math.random() * 5) + 5 },
                { id: 'totalBudget', value: (Math.random() * 20 + 30).toFixed(1) + 'M MAD' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element) {
                    element.textContent = stat.value;
                    element.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        element.style.transform = 'scale(1)';
                    }, 200);
                }
            });

            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('fr-FR');
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 5) + 1;
        }

        function checkSystemStatus() {
            // Simulation de vérification du système
            const statusIndicator = document.getElementById('statusIndicator');
            const dbStatus = document.getElementById('dbStatus');

            // Animation de vérification
            statusIndicator.innerHTML = '🔄 Vérification...';
            statusIndicator.style.background = '#ed8936';

            setTimeout(() => {
                statusIndicator.innerHTML = '🟢 Opérationnel';
                statusIndicator.style.background = '#48bb78';
                dbStatus.innerHTML = 'MySQL - Connectée ✅';
            }, 1500);
        }

        function showLogin() {
            const modal = createModal('🔐 Connexion au Système', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Interface de connexion disponible !</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>🔑 Comptes par défaut :</strong><br>
                        • <strong>Super Admin:</strong> admin / admin123<br>
                        • <strong>Admin Préfecture:</strong> admin_khenifra / demo123<br>
                        • <strong>Admin Commune:</strong> admin_commune_khenifra / demo123
                    </div>

                    <p style="margin: 15px 0;">Pour activer la connexion complète :</p>
                    <ol style="margin-left: 20px; line-height: 1.6;">
                        <li>Configurez MySQL et exécutez database.sql</li>
                        <li>Démarrez le serveur backend Node.js</li>
                        <li>Utilisez l'interface de connexion</li>
                    </ol>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn">Compris</button>
                    </div>
                </div>
            `);
        }

        function showProjects() {
            const modal = createModal('📁 Gestion des Projets', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Module de gestion des projets</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>✨ Fonctionnalités disponibles :</strong><br>
                        • Création et modification de projets<br>
                        • Suivi des budgets et bénéficiaires<br>
                        • Localisation par commune<br>
                        • Gestion des marchés et financements<br>
                        • Rapports et statistiques
                    </div>

                    <div style="background: #e6fffa; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #38a169;">
                        <strong>🚀 Projets d'exemple :</strong><br>
                        • Construction Centre de Santé Rural<br>
                        • Réhabilitation École Primaire<br>
                        • Électrification Rurale<br>
                        • Adduction d'eau potable
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn green">Parfait</button>
                    </div>
                </div>
            `);
        }

        function showDocumentation() {
            const modal = createModal('📚 Documentation', `
                <div style="text-align: left; padding: 20px;">
                    <p style="margin-bottom: 15px;"><strong>Documentation complète du système</strong></p>

                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>📖 Guides disponibles :</strong><br>
                        • README.md - Guide d'installation<br>
                        • database.sql - Structure de la base<br>
                        • demo-data.sql - Données d'exemple<br>
                        • Configuration backend et frontend
                    </div>

                    <div style="background: #fff5f5; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #f56565;">
                        <strong>⚠️ Prérequis :</strong><br>
                        • Node.js (version 16+)<br>
                        • MySQL (version 8.0+)<br>
                        • Serveur web (Apache/Nginx)
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeModal()" class="btn orange">Compris</button>
                    </div>
                </div>
            `);
        }

        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 15px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        background: linear-gradient(135deg, #4299e1, #3182ce);
                        color: white;
                        padding: 20px;
                        border-radius: 15px 15px 0 0;
                        text-align: center;
                        font-size: 1.2em;
                        font-weight: 600;
                    ">
                        ${title}
                    </div>
                    ${content}
                </div>
            `;

            modal.onclick = (e) => {
                if (e.target === modal) closeModal();
            };

            document.body.appendChild(modal);
            return modal;
        }

        function closeModal() {
            const modals = document.querySelectorAll('div[style*="position: fixed"]');
            modals.forEach(modal => {
                if (modal.style.zIndex === '1000') {
                    modal.remove();
                }
            });
        }

        // Animation des cartes au chargement
        function animateCards() {
            const cards = document.querySelectorAll('.stat-card, .card, .feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.8s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        }

        // Animation des projets
        function animateProjects() {
            const projects = document.querySelectorAll('.project-item');
            projects.forEach((project, index) => {
                project.style.opacity = '0';
                project.style.transform = 'translateX(-20px)';
                setTimeout(() => {
                    project.style.transition = 'all 0.6s ease';
                    project.style.opacity = '1';
                    project.style.transform = 'translateX(0)';
                }, 1000 + (index * 200));
            });
        }

        // Effet de typing pour le titre
        function typeTitle() {
            const title = document.querySelector('.header h1');
            const text = title.textContent;
            title.textContent = '';

            let i = 0;
            const typeInterval = setInterval(() => {
                title.textContent += text[i];
                i++;
                if (i >= text.length) {
                    clearInterval(typeInterval);
                }
            }, 100);
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Démarrer les animations
            setTimeout(typeTitle, 500);
            setTimeout(animateCards, 1000);
            setTimeout(animateProjects, 2000);

            // Mettre à jour les données
            setTimeout(updateStats, 1500);
            setTimeout(checkSystemStatus, 2000);

            // Mise à jour périodique
            setInterval(updateStats, 10000);

            // Ajouter des effets hover dynamiques
            addHoverEffects();
        });

        function addHoverEffects() {
            // Effet sur les cartes de statistiques
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Effet sur les boutons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // Effet de parallaxe simple
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('body');
            const speed = scrolled * 0.3;
            parallax.style.backgroundPosition = `center ${speed}px`;
        });

        // Gestion du clavier
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
