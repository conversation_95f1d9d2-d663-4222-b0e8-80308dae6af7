# 🗄️ Guide d'installation MySQL pour Windows

## 📥 Installation de MySQL

### Option 1: MySQL Installer (Recommandé)
1. **Téléchargez MySQL Installer** depuis: https://dev.mysql.com/downloads/installer/
2. **Choisissez** "mysql-installer-web-community" (plus léger)
3. **Exécutez l'installateur** et sélectionnez "Developer Default"
4. **Configurez le mot de passe root** (laissez vide pour correspondre au .env)

### Option 2: XAMPP (Plus simple)
1. **Téléchargez XAMPP** depuis: https://www.apachefriends.org/
2. **Installez XAMPP** avec MySQL
3. **Démarrez MySQL** depuis le panneau de contrôle XAMPP

## 🔧 Configuration de la base de données

### Étape 1: Créer la base de données
```bash
# Ouvrir MySQL Command Line Client ou phpMyAdmin
mysql -u root -p

# Créer la base de données
CREATE DATABASE GestionProjets;
exit;
```

### Étape 2: Importer le schéma
```bash
# Dans le dossier du projet
mysql -u root -p GestionProjets < database.sql
```

### Étape 3: Créer l'utilisateur admin
```bash
cd backend
npm run init-db
```

## ⚙️ Configuration du fichier .env

Vérifiez que votre fichier `.env` contient:
```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=GestionProjets
JWT_SECRET=votre_secret_jwt_tres_securise_ici
PORT=5000
```

## 🔍 Vérification de l'installation

### Test de connexion MySQL:
```bash
mysql -u root -p -e "SHOW DATABASES;"
```

### Test des tables:
```bash
mysql -u root -p GestionProjets -e "SHOW TABLES;"
```

## 🚨 Résolution des problèmes courants

### Erreur "Access denied"
- Vérifiez le mot de passe root MySQL
- Modifiez `DB_PASSWORD` dans le fichier `.env`

### Erreur "Database does not exist"
- Exécutez: `mysql -u root -p -e "CREATE DATABASE GestionProjets;"`
- Puis importez: `mysql -u root -p GestionProjets < database.sql`

### Port 3306 occupé
- Arrêtez les autres services MySQL
- Ou changez le port dans la configuration MySQL

## ✅ Validation finale

Une fois MySQL configuré, vous devriez pouvoir:
1. ✅ Vous connecter à MySQL
2. ✅ Voir la base `GestionProjets`
3. ✅ Voir les tables créées
4. ✅ Démarrer le backend sans erreur
