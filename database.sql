-- Création de la base de données
CREATE DATABASE IF NOT EXISTS GestionProjets;
USE GestionProjets;

-- ---------------------------
-- Tables des divisions administratives
-- ---------------------------

CREATE TABLE Regions (
    id_region INT PRIMARY KEY AUTO_INCREMENT,
    nom_region VARCHAR(100) NOT NULL
);

CREATE TABLE Prefectures (
    id_prefecture INT PRIMARY KEY AUTO_INCREMENT,
    nom_prefecture VARCHAR(100) NOT NULL,
    id_region INT NOT NULL,
    FOREIGN KEY (id_region) REFERENCES Regions(id_region)
);

CREATE TABLE Communes (
    id_commune INT PRIMARY KEY AUTO_INCREMENT,
    nom_commune VARCHAR(100) NOT NULL,
    id_prefecture INT NOT NULL,
    FOREIGN KEY (id_prefecture) REFERENCES Prefectures(id_prefecture)
);

-- ---------------------------
-- Table des rôles utilisateurs
-- ---------------------------

CREATE TABLE Roles (
    id_role INT PRIMARY KEY AUTO_INCREMENT,
    nom_role VARCHAR(50) NOT NULL,
    description VARCHAR(255)
);

-- ---------------------------
-- Table des utilisateurs
-- ---------------------------

CREATE TABLE Utilisateurs (
    id_utilisateur INT PRIMARY KEY AUTO_INCREMENT,
    nom_utilisateur VARCHAR(50) NOT NULL UNIQUE,
    mot_de_passe_hash VARCHAR(255) NOT NULL,
    nom_complet VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    id_role INT NOT NULL,
    id_prefecture INT,
    id_commune INT,
    est_super_admin BOOLEAN DEFAULT FALSE,
    derniere_connexion DATETIME,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_role) REFERENCES Roles(id_role),
    FOREIGN KEY (id_prefecture) REFERENCES Prefectures(id_prefecture),
    FOREIGN KEY (id_commune) REFERENCES Communes(id_commune),
    CHECK ((id_prefecture IS NOT NULL AND id_commune IS NULL) OR
           (id_prefecture IS NULL AND id_commune IS NOT NULL) OR
           (id_prefecture IS NULL AND id_commune IS NULL))
);

-- ---------------------------
-- Table des projets
-- ---------------------------

CREATE TABLE Projets (
    id_projet INT PRIMARY KEY AUTO_INCREMENT,
    intitule_projet VARCHAR(255) NOT NULL,
    programme VARCHAR(100),
    annee INT,
    secteur VARCHAR(100),
    objectifs TEXT,
    consistance TEXT,
    nombre_beneficiaires INT,
    statut_foncier VARCHAR(100),
    situation_foncier VARCHAR(100),
    stade_etudes VARCHAR(100),
    activite_royale BOOLEAN DEFAULT FALSE,
    cout_global DECIMAL(15,2),
    statut VARCHAR(50),
    id_maitre_ouvrage INT,
    id_createur INT NOT NULL,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_mise_a_jour DATETIME ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_createur) REFERENCES Utilisateurs(id_utilisateur)
);

-- ---------------------------
-- Localisation des projets
-- ---------------------------

CREATE TABLE LocalisationProjets (
    id_localisation INT PRIMARY KEY AUTO_INCREMENT,
    id_projet INT NOT NULL,
    id_commune INT NOT NULL,
    quartiers_ou_douars VARCHAR(100),
    FOREIGN KEY (id_projet) REFERENCES Projets(id_projet) ON DELETE CASCADE,
    FOREIGN KEY (id_commune) REFERENCES Communes(id_commune)
);

-- ---------------------------
-- Sources de financement
-- ---------------------------

CREATE TABLE SourcesFinancement (
    id_financement INT PRIMARY KEY AUTO_INCREMENT,
    id_projet INT NOT NULL,
    partenaire VARCHAR(100),
    montant DECIMAL(15,2),
    annee INT,
    FOREIGN KEY (id_projet) REFERENCES Projets(id_projet) ON DELETE CASCADE
);

-- ---------------------------
-- Marchés liés aux projets
-- ---------------------------

CREATE TABLE Marches (
    id_marche INT PRIMARY KEY AUTO_INCREMENT,
    id_projet INT NOT NULL,
    intitule_marche VARCHAR(255),
    type_marche ENUM('Marché', 'Bon de commande', 'Convention'),
    description TEXT,
    credits_ouverts DECIMAL(15,2),
    delai_execution_mois INT,
    maitre_oeuvre VARCHAR(100),
    date_lancement_ao DATE,
    mode_lancement_ao VARCHAR(100),
    numero_ao VARCHAR(50),
    date_ouverture_plis DATE,
    statut_ao VARCHAR(50),
    attributaire VARCHAR(100),
    montant_attribution DECIMAL(15,2),
    numero_marche VARCHAR(50),
    credits_engages DECIMAL(15,2),
    date_engagement DATE,
    taux_engagement DECIMAL(5,2),
    date_visa DATE,
    date_approbation DATE,
    date_notification_approbation DATE,
    taux_avancement_physique DECIMAL(5,2),
    montant_emis DECIMAL(15,2),
    date_emission DATE,
    taux_emission DECIMAL(5,2),
    arret_travaux BOOLEAN DEFAULT FALSE,
    date_arret DATE,
    motif_arret TEXT,
    date_reprise DATE,
    responsable_suivi VARCHAR(100),
    statut_marche VARCHAR(50),
    date_reception_provisoire DATE,
    date_reception_definitive DATE,
    observations TEXT,
    FOREIGN KEY (id_projet) REFERENCES Projets(id_projet) ON DELETE CASCADE
);

-- ---------------------------
-- Journalisation des activités
-- ---------------------------

CREATE TABLE JournalActivites (
    id_journal INT PRIMARY KEY AUTO_INCREMENT,
    id_utilisateur INT,
    action VARCHAR(50) NOT NULL,
    table_concernee VARCHAR(50) NOT NULL,
    id_enregistrement INT,
    ancienne_valeur TEXT,
    nouvelle_valeur TEXT,
    date_action DATETIME DEFAULT CURRENT_TIMESTAMP,
    adresse_ip VARCHAR(45),
    FOREIGN KEY (id_utilisateur) REFERENCES Utilisateurs(id_utilisateur)
);

-- ---------------------------
-- Insertion des données de base
-- ---------------------------

-- Rôles
INSERT INTO Roles (nom_role, description) VALUES
('Super Admin', 'Accès complet à tous les projets et fonctions administratives'),
('Admin Préfecture', 'Peut gérer les projets dans sa préfecture'),
('Admin Commune', 'Peut gérer les projets dans sa commune'),
('Utilisateur Standard', 'Peut consulter les projets selon son niveau d''accès');

-- Régions
INSERT INTO Regions (nom_region) VALUES ('Région 1'), ('Région 2');

-- Préfectures
INSERT INTO Prefectures (nom_prefecture, id_region) VALUES
('Khénifra', 1),
('Autre Préfecture', 2);

-- Communes
INSERT INTO Communes (nom_commune, id_prefecture) VALUES
('Commune A', 1),
('Commune B', 1),
('Commune C', 2);

-- ---------------------------
-- Vue d'accès aux projets
-- ---------------------------

CREATE VIEW VueAccesProjets AS
SELECT
    p.*,
    lp.id_commune,
    c.id_prefecture
FROM
    Projets p
JOIN
    LocalisationProjets lp ON p.id_projet = lp.id_projet
JOIN
    Communes c ON lp.id_commune = c.id_commune;

-- ---------------------------
-- Procédure stockée pour l'accès utilisateur
-- ---------------------------

DELIMITER //

CREATE PROCEDURE ObtenirProjetsUtilisateur(IN id_utilisateur_param INT)
BEGIN
    DECLARE role_utilisateur VARCHAR(50);
    DECLARE id_prefecture_utilisateur INT;
    DECLARE id_commune_utilisateur INT;
    DECLARE est_super BOOLEAN;

    -- Obtenir les informations de l'utilisateur
    SELECT r.nom_role, u.id_prefecture, u.id_commune, u.est_super_admin
    INTO role_utilisateur, id_prefecture_utilisateur, id_commune_utilisateur, est_super
    FROM Utilisateurs u
    JOIN Roles r ON u.id_role = r.id_role
    WHERE u.id_utilisateur = id_utilisateur_param;

    -- Retourner les projets selon le rôle
    IF est_super OR role_utilisateur = 'Super Admin' THEN
        SELECT * FROM Projets;

    ELSEIF role_utilisateur = 'Admin Préfecture' AND id_prefecture_utilisateur IS NOT NULL THEN
        SELECT p.*
        FROM Projets p
        JOIN LocalisationProjets lp ON p.id_projet = lp.id_projet
        JOIN Communes c ON lp.id_commune = c.id_commune
        WHERE c.id_prefecture = id_prefecture_utilisateur;

    ELSEIF role_utilisateur = 'Admin Commune' AND id_commune_utilisateur IS NOT NULL THEN
        SELECT p.*
        FROM Projets p
        JOIN LocalisationProjets lp ON p.id_projet = lp.id_projet
        WHERE lp.id_commune = id_commune_utilisateur;

    ELSE
        SELECT 'Aucun projet accessible pour cet utilisateur' AS message;
    END IF;
END //

DELIMITER ;
